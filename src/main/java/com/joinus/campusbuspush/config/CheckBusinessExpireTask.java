package com.joinus.campusbuspush.config;

import cn.hutool.json.JSONUtil;
import com.joinus.campusbuspush.entity.BusCardBusiness;
import com.joinus.campusbuspush.kafka.KafkaProducer;
import com.joinus.campusbuspush.service.BusCarBusinessService;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.List;

/**
 * @program: campus-bus-push
 * 检查业务到期时间的定时器
 * @author: zxr
 * @create: 2020-05-21 11:10
 **/
@Configuration
@EnableScheduling
@AllArgsConstructor
public class CheckBusinessExpireTask {

    private BusCarBusinessService busCarBusinessService;
    private KafkaProducer kafkaProducer;

    /**
     * 每天20点执行,查询业务将到期的数据(7天,3天,1天)
     * @patams: []
     * @return: void
     * @Author: zxr
     * @Date: 2020/5/25 21:46
     */
    @Scheduled(cron = "0 0 20 * * *")
    private void checkExpire(){
        List<BusCardBusiness> expiringList = busCarBusinessService.selectExpiringBusiness();
        for (BusCardBusiness busCardBusiness : expiringList){
            kafkaProducer.sendAboutToExpire(JSONUtil.toJsonStr(busCardBusiness));
        }
    }

}
