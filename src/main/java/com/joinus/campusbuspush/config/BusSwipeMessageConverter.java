package com.joinus.campusbuspush.config;

import com.joinus.campusbuspush.entity.BusSwipeMessage;
import com.joinus.campusbuspush.entity.dto.ConCommunication;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * 转换器
 * <AUTHOR>
 * @description
 * @date 2021/6/28
 */
@Mapper(componentModel = "spring")
public interface BusSwipeMessageConverter {

    @Mappings({
            @Mapping(source = "studentId", target = "attrib_01"),
            @Mapping(source = "busLineNo", target = "attrib_02"),
            @Mapping(source = "busStation", target = "attrib_03"),
            @Mapping(source = "studentName", target = "attrib_05"),
            @Mapping(source = "cardNo", target = "attrib_06"),
            @Mapping(source = "openId", target = "attrib_07"),
            @Mapping(source = "busNo", target = "attrib_08"),
            @Mapping(source = "amountType", target = "attrib_09"),
            @Mapping(source = "swipeTimeStamp", target = "attrib_11"),
            @Mapping(source = "swipeType", target = "attrib_15"),
            @Mapping(source = "direction", target = "attrib_16"),
            @Mapping(source = "amount", target = "attrib_17"),
            @Mapping(source = "balance", target = "attrib_18")
    })
    ConCommunication swipeMessageToConCommunication(BusSwipeMessage busSwipeMessage);

    @Mappings({
            @Mapping(target = "studentId", source = "attrib_01"),
            @Mapping(target = "busLineNo", source = "attrib_02"),
            @Mapping(target = "busStation", source = "attrib_03"),
            @Mapping(target = "studentName", source = "attrib_05"),
            @Mapping(target = "cardNo", source = "attrib_06"),
            @Mapping(target = "openId", source = "attrib_07"),
            @Mapping(target = "busNo", source = "attrib_08"),
            @Mapping(target = "amountType", source = "attrib_09"),
            @Mapping(target = "swipeTimeStamp", source = "attrib_11"),
            @Mapping(target = "swipeType", source = "attrib_15"),
            @Mapping(target = "direction", source = "attrib_16"),
            @Mapping(target = "amount", source = "attrib_17"),
            @Mapping(target = "balance", source = "attrib_18")
    })
    BusSwipeMessage conCommunicationToSwipeMessage(ConCommunication conCommunication);
}
