package com.joinus.campusbuspush.config;

import com.baomidou.mybatisplus.extension.incrementer.OracleKeyGenerator;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @program: campus-bus-push
 * Mybatis-plus的配置Bean
 * @author: zxr
 * @create: 2020-05-13 09:59
 **/
@Configuration
@MapperScan("com.joinus.campusbuspush.mapper")
public class MybatisPlusConfig {


    /**
     * 配置分页插件
     * @return: PaginationInterceptor
     * @Author: zxr
     * @Date: 2020/5/13 10:02
     */
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        // 设置最大单页限制数量，默认 500 条，-1 不受限制
        paginationInterceptor.setLimit(500);
        return paginationInterceptor;
    }

    /**
     * 开启id序列生成策略
     * @return: OracleKeyGenerator
     * @Author: zxr
     * @Date: 2020/5/13 10:02
     */
    @Bean
    public OracleKeyGenerator oracleKeyGenerator() {
        return new OracleKeyGenerator();
    }

}
