package com.joinus.campusbuspush.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * @program: campus-bus-push
 * Swagger配置类
 * @author: zxr
 * @create: 2020-05-18 08:54
 **/
@Configuration
@EnableSwagger2
@EnableKnife4j
public class SwaggerConfiguration {

    @Value("${swagger.enable}")
    private Boolean swaggerShow;
    @Bean
    public Docket api() {

        return new Docket(DocumentationType.SWAGGER_2)
                .enable(swaggerShow)
                .apiInfo(apiInfo()).select()
                //扫描所有有注解的api，用这种方式更灵活
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .paths(PathSelectors.any())
                .build();
    }

    /**
     * 获取swagger ApiInfo
     *
     * @return
     */
    ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("天迈-爱家校 校园公交 Swagger API 文档")
                .description("Api Documentation")
                .termsOfServiceUrl("www.967111.com")
                .version("1.0")
                .build();
    }
}
