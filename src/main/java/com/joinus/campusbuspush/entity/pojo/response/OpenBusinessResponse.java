package com.joinus.campusbuspush.entity.pojo.response;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @program: campus-bus-push
 * 开通业务返回数据封装
 * @author: zxr
 * @create: 2020-05-21 11:54
 **/
@Data
@AllArgsConstructor
@ApiModel(value = "开通业务返回数据实体")
public class OpenBusinessResponse {

    private Long studentId;

    private Long businessId;

    private String cardNo;

    private int couponSend;
}
