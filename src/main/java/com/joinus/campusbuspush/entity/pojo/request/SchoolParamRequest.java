package com.joinus.campusbuspush.entity.pojo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @program: campus-bus-push
 * 学校相关查询请求实体
 * @author: zxr
 * @create: 2020-05-15 14:42
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "学校相关查询请求实体")
public class SchoolParamRequest extends PageParamRequest{

    /**
     * 学校id
     */
    private Long schoolId;
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称", example = "郑州市第九中学")
    private String SchoolName;
    /**
     * 学校名称简拼
     */
    @ApiModelProperty(value = "学校名称简拼", example = "zzsdjzx")
    private String SchoolNameJp;
    /**
     * 学校名称全拼
     */
    @ApiModelProperty(value = "学校名称全拼", example = "zhengzhoushidijiuzhongxue")
    private String SchoolNameQp;
    /**
     * 年级id
     */
    @ApiModelProperty("年级id")
    private Long gradeId;

}
