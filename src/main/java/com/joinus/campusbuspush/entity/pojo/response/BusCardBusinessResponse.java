package com.joinus.campusbuspush.entity.pojo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @program: campus-bus-push
 * 公交卡业务返回字段封装
 * @author: zxr
 * @create: 2020-05-18 17:54
 **/
@Data
@ApiModel(value = "公交卡业务返回字段实体")
public class BusCardBusinessResponse {


    /**
     * 业务id
     */
    private Long id;
    /**
     * 公交卡号
     */
    private String[] cardNoArr;
    /**
     * 公交卡余额
     */
    private String balance;
    /**
     * 学生id
     */
    private Long studentId;
    /**
     * 学生姓名
     */
    private String studentName;
    /**
     * 公交业务有效期结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date busEndTime;
    /**
     * 地铁业务有效期结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date subwayEndTime;

}
