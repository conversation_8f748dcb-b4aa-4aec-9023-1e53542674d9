package com.joinus.campusbuspush.entity.pojo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;

/**
 * @program: campus-bus-push
 * 开通业务请求参数
 * @author: zxr
 * @create: 2020-05-18 20:15
 **/
@Data
@ApiModel(value = "业务相关查询请求实体")
@Accessors(chain = true)
public class OpenBusinessParamRequest {

    /**
     * 学生id
     */
    @ApiModelProperty(value = "学生id", example = "3805031")
    private Long studentId;
    /**
     * 微信openId
     */
    @ApiModelProperty(value = "微信openId")
    private String openId;
    /**
     * 学生姓名
     */
    @Size(max = 20, message = "学生姓名过长, 最大20个字符")
    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    /**
     * 联系人手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phoneNo;

    /**
     * 验证码
     */
    @ApiModelProperty(value = "验证码")
    private String code;
    /**
     * 公交卡号
     */
    @ApiModelProperty(value = "公交卡号")
    private String cardNo;
    /**
     * 公交卡密码
     */
    @ApiModelProperty(value = "公交卡密码", hidden = true)
    private String cardPassword;
    /**
     * 开通的业务id
     */
    @ApiModelProperty(value = "开通的业务id", example = "285")
    private Long productId;
    /**
     * 学校id
     */
    @ApiModelProperty(value = "学校id", example = "1", hidden = true)
    private Long schoolId;
    /**
     * 班级id
     */
    @ApiModelProperty(value = "班级id", example = "1", hidden = true)
    private Long classId;
    /**
     * 公交卡类型 0、学生卡 1、老年卡
     **/
    @ApiModelProperty(value = "公交卡类型0、学生卡 1、老年卡", example = "0", hidden = true)
    private Integer cardType;
    /**
     * 关系
     **/
    @ApiModelProperty(value = "关系")
    private String childRelation;
}
