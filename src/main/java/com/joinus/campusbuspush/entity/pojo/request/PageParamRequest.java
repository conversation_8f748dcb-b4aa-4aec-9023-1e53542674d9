package com.joinus.campusbuspush.entity.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: campus-bus-push
 * 分页查询参数
 * @author: zxr
 * @create: 2020-05-13 15:49
 **/

@Data
public class PageParamRequest {

    @ApiModelProperty(value = "分页参数-页码", example = "10", position = 100)
    private Long size = 20L;
    @ApiModelProperty(value = "分页参数-当前页", example = "1", position = 99)
    private Long current = 0L;

    /**
     * 主键id
     */
    private Long id;
}
