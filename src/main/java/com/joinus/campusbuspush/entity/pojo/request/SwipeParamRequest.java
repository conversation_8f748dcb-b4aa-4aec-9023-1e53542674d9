package com.joinus.campusbuspush.entity.pojo.request;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @program: campus-bus-push
 * 公交卡请求参数封装
 * @author: zxr
 * @create: 2020-05-16 16:48
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "公交卡请求参数实体")
public class SwipeParamRequest extends PageParamRequest {

    /**
     * 公交卡号
     */
    private String cardNo;
    /**
     * 刷卡时间
     */
    private String swipeTime;
    /**
     * 公交车号
     */
    private String busNo;
    /**
     * 公交卡余额
     */
    private String balance;
    /**
     * 消费金额
     */
    private String amount;
    /**
     * 公交车线路号
     */
    private String busLineNo;
    /**
     * 上车站点
     */
    private String busStation;

}
