package com.joinus.campusbuspush.entity.pojo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 优惠券查询参数类
 * @date 2020/11/18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "优惠券查询请求实体")
public class CouponParamRequest extends PageParamRequest{
    @ApiModelProperty(value = "兑换码")
    private String couponCode;
    @ApiModelProperty(value = "金额", example = "5")
    private Integer couponAmount;
    @ApiModelProperty(value = "创建时间起")
    private String createDateBegin;
    @ApiModelProperty(value = "创建时间止")
    private String createDateEnd;
    @ApiModelProperty(value = "兑换时间起")
    private String exchangeDateBegin;
    @ApiModelProperty(value = "兑换时间止")
    private String exchangeDateEnd;
    @ApiModelProperty(value = "使用时间起")
    private String useDateBegin;
    @ApiModelProperty(value = "使用时间止")
    private String useDateEnd;
    @ApiModelProperty(value = "优惠券状态", example = "0")
    private Integer couponStatus;
    @ApiModelProperty(value = "手机号")
    private String phoneNo;
}
