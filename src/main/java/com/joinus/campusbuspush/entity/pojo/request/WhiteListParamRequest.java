package com.joinus.campusbuspush.entity.pojo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @program: campus-bus-push
 * 白名单相关查询请求实体
 * @author: zxr
 * @create: 2020-05-15 14:42
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "白名单相关查询请求实体")
public class WhiteListParamRequest extends PageParamRequest{

    /**
     * 最后的用户业务表id
     */
    private Long lastId;
    /**
     * 最后的更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateTime;


}
