package com.joinus.campusbuspush.entity.pojo.response;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 优惠券展示类
 * @date 2020/11/18
 */
@Data
@ApiModel(value = "优惠券返回字段实体")
public class CouponResponse {
    private Long id;
    private String couponCode;
    private int couponAmount;
    private String createTime;
    private Date exchangeStartTime;
    private Date exchangeEndTime;
    private Date useStartTime;
    private Date useEndTime;
    private int couponStatus;
    private String studentName;
    private String phoneNo;
    private String productName;
    private BigDecimal totalFee;
    private String exchangeTimePeriod;
    private String useTimePeriod;
}
