package com.joinus.campusbuspush.entity.pojo.request;


import com.joinus.campusbuspush.common.annotations.PhoneValidated;
import lombok.Data;

import java.io.Serializable;

@Data
public class SmsRequest implements Serializable {

    private String appCode;
    // 手机号
    @PhoneValidated
    private String phoneNum;
    // 验证码短信类型 0:登录/注册,1:H5登录/注册,2:绑定手机号,3:修改手机号
    private int yzmType;
    // 待校验验证码
    private String toBeVerifiedYzm;

    private String templateId;

    private String extraData;

    private int isH5Scene;

    private String ticket;

    private String sig;

    private String token;

    private String scene;

    private String sessionId;

}
