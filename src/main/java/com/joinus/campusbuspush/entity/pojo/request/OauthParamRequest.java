package com.joinus.campusbuspush.entity.pojo.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @program: campus-bus-push
 * oauth认证参数
 * @author: zxr
 * @create: 2020-05-15 16:52
 **/
@Data
public class OauthParamRequest {

    /**
     * 请求类型
     */
    @NotEmpty(message="请求类型不能为空！")
    private String grant_type;
    /**
     * 客户端id
     */
    @NotEmpty(message="请求类型不能为空！")
    private String client_id;
    /**
     * 客户端secret
     */
    @NotEmpty(message="请求类型不能为空！")
    private String client_secret;

}
