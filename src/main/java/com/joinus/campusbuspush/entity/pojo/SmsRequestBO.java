package com.joinus.campusbuspush.entity.pojo;


import lombok.Data;

@Data
public class SmsRequestBO {

    private String appCode;
    // 手机号
    private String phoneNum;
    // 验证码短信类型 0:登录/注册,1:H5登录/注册,2:绑定手机号,3:修改手机号
    private int yzmType;
    // 待校验验证码
    private String toBeVerifiedYzm;

    private String templateId;

    private String extraData;

    private int isH5Scene;

    private String ticket;

    private String templateContentParam;

    private String templateContent;



    public SmsRequestBO(String appCode, String phoneNum, int yzmType, String toBeVerifiedYzm) {
        this.appCode = appCode;
        this.phoneNum = phoneNum;
        this.yzmType = yzmType;
        this.toBeVerifiedYzm = toBeVerifiedYzm;
    }

    public SmsRequestBO(String appCode, String phoneNum, int yzmType, String templateId, String extraData,
                        int isH5Scene) {
        this.appCode = appCode;
        this.phoneNum = phoneNum;
        this.yzmType = yzmType;
        this.templateId = templateId;
        this.extraData = extraData;
        this.isH5Scene = isH5Scene;
    }

    public SmsRequestBO(String appCode, String phoneNum, String  templateId, String templateContentParam, String templateContent) {
        this.appCode = appCode;
        this.phoneNum = phoneNum;
        this.templateId = templateId;
        this.templateContentParam = templateContentParam;
        this.templateContent = templateContent;
    }


    public SmsRequestBO(String appCode, String phoneNum, int yzmType, String templateId, String extraData,
                        int isH5Scene, String ticket) {
        this.appCode = appCode;
        this.phoneNum = phoneNum;
        this.yzmType = yzmType;
        this.templateId = templateId;
        this.extraData = extraData;
        this.isH5Scene = isH5Scene;
        this.ticket = ticket;
    }
}
