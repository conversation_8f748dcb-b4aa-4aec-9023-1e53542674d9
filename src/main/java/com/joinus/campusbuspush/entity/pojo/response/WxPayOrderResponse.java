package com.joinus.campusbuspush.entity.pojo.response;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @program: campus-bus-push
 * 微信支付下单返回数据
 * @author: zxr
 * @create: 2020-05-21 10:25
 **/
@Data
@ApiModel(value = "微信支付下单返回数据")
public class WxPayOrderResponse {
    /**
     * 公众号ID
     */
    private String appId;
    /**
     * 时间戳，单位秒
     */
    private long timeStamp;
    /**
     * 随机字符串
     */
    private String nonceStr;
    /**
     * 签名方式，默认MD5
     */
    private String signType = "MD5";
    /**
     * 订单详情扩展字符串
     */
    private String ppackage;
    /**
     * 签名数据
     */
    private String paySign;
    /**
     * 订单号
     */
    private String orderNo;
}
