package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * @program: campus-bus-push
 * 公交卡业务关系绑定
 * @author: zxr
 * @create: 2020-08-19 13:49
 **/
@Data
@TableName("T_BUS_CARD_BUSINESS_BINDING")
public class BusCardBusinessBinding extends Model {
    private static final long serialVersionUID = -8641127685349501205L;

    /**
     * 关联业务表id
     */
    private Long businessId;
    /**
     * 关联卡信息id
     */
    private Long cardId;
    /**
     * 是否启用
     */
    private Integer useStatus;
}
