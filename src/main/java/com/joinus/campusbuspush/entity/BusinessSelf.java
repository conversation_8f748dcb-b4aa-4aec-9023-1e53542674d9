package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @program: campus-bus-push
 * 自推广业务开通表
 * @author: zxr
 * @create: 2020-06-10 15:21
 **/
@Data
@Accessors(chain = true)
@TableName("T_BUSINESS_SELF")
@KeySequence("SEQ_T_BUSINESS_SELF")
@EqualsAndHashCode(callSuper = true)
public class BusinessSelf extends Model {
    private static final long serialVersionUID = 8559552028468757813L;

    private Long id;
    /**
     * 关联产品表id
     */
    private String productId;
    /**
     * 业务类型 0:基础业务;2:乘车安
     */
    private String businessType;
    /**
     * 关联业务表id
     */
    private String businessId;
    /**
     * 关联子产品表id
     */
    private String subProductId;
    /**
     * 学生id
     */
    private String studentId;
    /**
     * 学校id
     */
    private String schoolId;
    /**
     * 年级id
     */
    private String gradeId;
    /**
     * 班级id
     */
    private String classId;
    /**
     * 家长手机号
     */
    private String phoneNum;
    /**
     * 手机号归属地
     */
    private String telSegment;
    /**
     * 业务开始时间
     */
    private Date startTime;
    /**
     * 业务结束时间
     */
    private Date endTime;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
