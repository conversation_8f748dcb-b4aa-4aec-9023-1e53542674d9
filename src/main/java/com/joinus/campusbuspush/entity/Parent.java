package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @program: campus-bus-push
 * 家长实体
 * @author: zxr
 * @create: 2020-05-28 14:28
 **/
@Data
@TableName("T_PARENT")
@KeySequence(value = "SEQ_T_PARENT")
@EqualsAndHashCode(callSuper = true)
public class Parent extends Model {

    private static final long serialVersionUID = -4199544208438677803L;
    private Long id;
    private Date addTime ;
    private String loginIp;
    private Date loginTime;
    private Integer num;
    private String parentName ;
    private String password ;
    private String telNum ;
    /**
     * 国籍
     */
    private Integer nationId ;
    private Integer telType;
    private String telSegment;

    /**
     * 逻辑删除字段
     */
    @TableLogic(value = "1", delval = "0")
    @TableField(value = "isactive",fill = FieldFill.INSERT)
    private Integer isActive;
}
