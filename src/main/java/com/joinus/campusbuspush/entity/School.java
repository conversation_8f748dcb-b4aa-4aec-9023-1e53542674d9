package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @program: campus-bus-push
 * 学校实体类
 * @author: zxr
 * @create: 2020-05-11 09:59
 **/
@Data
@TableName("T_SCHOOL")
@KeySequence(value = "SEQ_T_SCHOOL")
@EqualsAndHashCode(callSuper = true)
public class School extends BaseEntity {
    private static final long serialVersionUID = 4067361575038894032L;

    private Long id;
    /**
     * 业务变更时间
     */
    private Date chargeTime;
    /**
     * 学校编号 (集团id)
     */
    private String schoolCode;
    /**
     * 学校描述
     */
    private String schoolDesc;

    private String schoolName;
    /**
     * 小学、初中、高中
     */
    private Integer schoolTypeId;
    /**
     * 区域id,对应sys_region_new表的region_id
     */
    private String newRegionId;
    /**
     * 学校名称全拼
     */
    private String schoolNameQp;
    /**
     * 学校名称简拼
     */
    @JsonIgnore
    private String schoolNameJp;


}
