package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @program: campus-bus-push
 * 已开业务白名单
 * @author: zxr
 * @create: 2020-05-18 10:34
 **/
@Data
@TableName("T_BUS_CARD_WHITE_LIST")
@KeySequence("SEQ_T_BUS_CARD_WHITE_LIST")
@EqualsAndHashCode(callSuper = true)
public class BusCardWhiteList extends BaseEntity {

    private static final long serialVersionUID = 7722604551836213192L;
    private Long id;
    private String cardNo;
    private String phoneNo;
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

}
