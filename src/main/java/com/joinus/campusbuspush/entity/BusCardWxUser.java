package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.campusbuspush.common.enums.SubChannel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @program: campus-bus-push
 * 乘车安微信关注用户
 * @author: zxr
 * @create: 2020-05-15 10:25
 **/
@Data
@TableName("T_BUS_CARD_WX_USER")
@KeySequence("SEQ_T_BUS_CARD_WX_USER")
@EqualsAndHashCode(callSuper = true)
public class BusCardWxUser extends BaseEntity{

    private static final long serialVersionUID = 1790946589864256732L;

    private Long id;

    private Long wxUserId;

    private String openId;
    /**
     * 用户关注状态(0:未关注;1:已关注)
     */
    private Integer subscribeStatus;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 用户关注的渠道来源
     * @see SubChannel
     */
    private Integer subChannel;
}
