package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Data
@Builder
@TableName("T_WX_USER")
@KeySequence("SEQ_T_WX_USER")
public class WxUser{
    private Long id;

    private String openid;

    private String nickname;

    private Integer sex;

    private String province;

    private String city;

    private String country;

    private String headimgurl;

    private String unionId;

    private String phone;

    private Integer userType;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
