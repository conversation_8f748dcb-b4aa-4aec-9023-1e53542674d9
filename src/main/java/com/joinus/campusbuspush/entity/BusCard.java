package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @program: campus-bus-push
 * 公交卡信息实体类
 * @author: zxr
 * @create: 2020-05-13 11:11
 **/
@Data
@TableName("T_BUS_CARD_INFO")
@KeySequence(value = "SEQ_T_BUS_CARD_INFO")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class BusCard extends BaseEntity{

    private static final long serialVersionUID = 5140925776229326592L;

    private Long id;
    /**
     * 公交卡号
     */
    private String cardNo;

    /**
     * 卡使用状态(0:停用;1:启用)
     */
    @TableField(exist = false)
    private Integer useStatus;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 逻辑卡号，用于给天迈传白名单
     **/
    private String logicCardNo;
}
