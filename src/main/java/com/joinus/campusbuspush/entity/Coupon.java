package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_BUS_CARD_BUSINESS_COUPON")
@KeySequence(value = "SEQ_T_BUS_CARD_BUSINESS_COUPON")
@Builder
public class Coupon implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("ID")
    private Long id;

    /**
     * 兑换码
     */
    @TableField("COUPON_CODE")
    private String couponCode;

    /**
     * 优惠券金额
     */
    @TableField("COUPON_AMOUNT")
    private Integer couponAmount;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;

    /**
     * 兑换有效期起
     */
    @TableField("EXCHANGE_START_TIME")
    private Date exchangeStartTime;

    /**
     * 兑换有效期止
     */
    @TableField("EXCHANGE_END_TIME")
    private Date exchangeEndTime;

    /**
     * 使用有效期起
     */
    @TableField("USE_START_TIME")
    private Date useStartTime;

    /**
     * 使用有效期止
     */
    @TableField("USE_END_TIME")
    private Date useEndTime;

    /**
     * 状态：0.未兑换 1.已兑换未使用 2.已使用
     */
    @TableField("COUPON_STATUS")
    private Integer couponStatus;

    /**
     * 用户微信openid
     */
    @TableField("OPEN_ID")
    private String openId;

    /**
     * t_bus_card_pay_order表ID
     */
    @TableField("ORDER_ID")
    private Long orderId;


}
