package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @program: campus-bus-push
 * @description: 业务产品 混合套餐分成比例
 * @author: zxr
 * @create: 2020-08-24 11:22
 **/
@Data
@TableName("T_PRODUCT_SELF_RATIO_DETAIL")
@EqualsAndHashCode(callSuper = true)
public class ProductSelfRatio extends Model {
    private static final long serialVersionUID = 7344438144180326706L;

    /**
     * 关联T_PRODUCT_SELF 表id, 表示所属父级产品套餐
     */
    private Long parentProductId;
    /**
     * 关联T_PRODUCT_SELF 表id
     */
    private Long productId;
    /**
     * 分成比例
     */
    private double ratio;
}
