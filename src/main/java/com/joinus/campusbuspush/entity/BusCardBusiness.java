package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @program: campus-bus-push
 * 已开通公交卡业务实体类
 * @author: zxr
 * @create: 2020-05-13 17:09
 **/
@Data
@TableName("T_BUS_CARD_BUSINESS")
@KeySequence("SEQ_T_BUS_CARD_BUSINESS")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class BusCardBusiness extends BaseEntity {

    private static final long serialVersionUID = -1445910277728211685L;

    private Long id;
    /**
     * 公交卡号
     */
    @TableField(exist=false )
    private String[] cardNoArr;
    /**
     * 余额
     */
    @TableField(exist=false )
    private Double balance;
    /**
     * 关联已开通业务的id
     */
    private Long productId;

    private Long studentId;

    @TableField(exist=false )
    private String studentName;
    /**
     * 开通业务家长的手机号
     */
    private String phoneNo;
    /**
     * 开通业务家长的openId
     */
    private String openId;
    /**
     * 开通业务家长的unionId
     */
    private String unionId;
    /**
     * 开通业务家长姓名
     */
    private String parentName;
    /**
     * 开通业务家长的身份证号
     */
    private String parentIdentity;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 地铁业务结束时间
     */
    private Date subwayEndTime;
    /**
     * 公交业务结束时间
     **/
    private Date busEndTime;
    /**
     * 是否赠送过产品套餐
     **/
    private Integer productGifted;
}
