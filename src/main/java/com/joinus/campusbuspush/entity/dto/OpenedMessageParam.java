package com.joinus.campusbuspush.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 开通业务成功微信消息模板
 * @Author: zxr
 * @Date: 2020/5/14 21:20
 */
@Data
@Accessors(chain = true)
public class OpenedMessageParam {
    /**
     * 消息模板id
     */
    private String templateId;
    /**
     * 微信用户openId
     */
    private String openId;

    private String first;
    private String remark;
    private String colorRemark;

    private String keyword1;
    private String keyword2;
    private String keyword3;
    private String keyword4;
    private String keyword5;

    private String colorKeyword1;
    private String colorKeyword2;
    private String colorKeyword3;
    private String colorKeyword4;

    /**
     * 点击消息跳转地址
     */
    private String url;
}
