package com.joinus.campusbuspush.entity.dto;

import lombok.Data;

/**
 * 乘车信息模型,S_CON_COMMUNICATION
 * 最多42个字段
 *
 * @params:
 * @return:
 * @Author: jxt
 * @Date: 2021/6/28 9:44 上午
 */
@Data
public class ConCommunication {

    private String attrib_01;

    private String attrib_02;

    private String attrib_03;

    private String attrib_04;

    private String attrib_05;

    private String attrib_06;

    private String attrib_07;

    private String attrib_08;

    private String attrib_09;

    private String attrib_10;

    private String attrib_11;

    private String attrib_12;

    private String attrib_13;

    private String attrib_14;

    private String attrib_15;

    private String attrib_16;

    private String attrib_17;

    private String attrib_18;

    private String attrib_19;

    private String attrib_20;

    private String attrib_21;

    private String attrib_22;

    private String attrib_23;

    private String attrib_24;

    private String attrib_25;

    private String attrib_26;

    private String attrib_27;

    private String attrib_28;

    private String attrib_29;

    private String attrib_30;

    private String attrib_31;

    private String attrib_32;

    private String attrib_33;

    private String attrib_34;

    private String attrib_35;

    private String attrib_36;

    private String attrib_37;

    private String attrib_38;

    private String attrib_39;

    private String attrib_40;

    private String attrib_41;

    private String attrib_42;
}
