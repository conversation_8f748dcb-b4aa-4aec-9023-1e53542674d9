package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @program: campus-bus-push
 * 学生实体类
 * @author: zxr
 * @create: 2020-05-11 09:55
 **/
@Data
@TableName("T_STUDENT")
@KeySequence(value = "SEQ_T_STUDENT")
@EqualsAndHashCode(callSuper = true)
public class Student extends Model{
    private static final long serialVersionUID = 1846636202964638127L;

    private Long id;
    /**
     * 卡号
     */
    @TableField(value = "CARDCODE")
    private String cardCode;
    /**
     * 身份证号
     */
    private String identity;
    /**
     * 年龄
     */
    private String inYear;
    /**
     * 区域id
     */
    private Integer regionId;

    private Long schoolId;

    private String studentName;

    private Long classId;

    /**
     * 是否坐校车，0 无效 1有效
     */
    @TableField(value = "ISBUS")
    private Integer isBus;
    /**
     * 是否住宿生，0 无效 1有效 2待定
     */
    @TableField(value = "ISDORM")
    private Integer isDorm;

    /**
     * 0.删除 1.有效
     **/
    @TableField(value = "ISACTIVE")
    private Integer isActive;
}
