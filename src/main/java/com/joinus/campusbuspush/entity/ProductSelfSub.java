package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @program: campus-bus-push
 * 业务产品表
 * @author: zxr
 * @create: 2020-05-14 09:33
 **/
@Data
@TableName("T_PRODUCT_SELF_SUB")
@KeySequence("SEQ_T_PRODUCT_SELF_SUB")
@EqualsAndHashCode(callSuper = true)
public class ProductSelfSub extends Model {
    private static final long serialVersionUID = -3960043488700896835L;

    /**
     * 业务子产品id
     */
    private Long id;
    /**
     * 业务类型:0:基础业务,1:计费打电话,2:乘车安
     */
    @TableField(exist=false)
    private String productType;
    /**
     * 所属父级产品id 关联T_PRODUCT_SELF 主键id
     */
    private Long productId;
    /**
     * 业务产品名称
     */
    @TableField(exist=false)
    private String productName;
    /**
     * 子业务产品名称
     */
    private String subProductName;
    /**
     * 子业务产品原费用(分)
     */
    private int originalFee;
    /**
     * 子业务产品现费用(分)
     */
    private int currentFee;
    /**
     * 业务有效时期(天)
     */
    private Integer period;
    /**
     * 产品描述
     */
    @TableField(exist=false)
    private String productDesc;
    /**
     * 0:不关联基础业务,1:关联基础业务
     */
    private String isRelatedBasicBiz;
    /**
     * 赠送时长(分钟)
     */
    private Integer givenDuration;
    /**
     * 通话叠加包配置(金额(分)/分钟)
     */
    private String callPkgConfig;
    /**
     * 每分钟花费(分)
     */
    private Integer costPerMinute;
    /**
     * 0:仅可拨打亲情号,1:可拨打电话不限(仅针对刷卡打电话)
     */
    private Integer callLimitType;
    /**
     * 0:下架,1:上架
     */
    @TableField(exist=false)
    private String state;

    private Date createTime;

    private Date updateTime;


}
