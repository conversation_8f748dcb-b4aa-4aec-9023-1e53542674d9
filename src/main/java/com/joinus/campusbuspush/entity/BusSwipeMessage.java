package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @program: campus-bus-push
 * 公交刷卡信息实体类
 * @author: zxr
 * @create: 2020-05-14 09:55
 **/
@Data
@TableName("T_BUS_SWIPE_MESSAGE")
@KeySequence("SEQ_T_BUS_SWIPE_MESSAGE")
@EqualsAndHashCode(callSuper = true)
public class BusSwipeMessage extends BaseEntity{
    private static final long serialVersionUID = -6858872368529851477L;

    private Long id;
    /**
     * 刷卡类型(默认 0:公交  1:地铁)
     */
    private int swipeType;
    /**
     * 公交卡号
     */
    @NotNull(message = "公交卡号不能为空")
    private String cardNo;

    private Long studentId;
    /**
     * 微信用户openId
     */
    private String openId;
    /**
     * 公交车牌号
     */
    private String busNo;
    /**
     * 公交车线路号
     */
    @NotNull(message = "线路号不能为空")
    private String busLineNo;
    /**
     * 站点信息
     */
    @NotNull(message = "站点信息不能为空")
    private String busStation;
    /**
     * 消费金额
     */
    private Double amount;
    /**
     * 卡余额
     */
    private Double balance;
    /**
     * 刷卡时间
     */
    @NotNull(message = "刷卡时间不能为空")
    @JsonFormat(pattern = "yyyyMMddHHmmss", timezone = "GMT+8")
    private Date swipeTime;
    /**
     * 刷卡时间戳
     **/
    @TableField(exist=false )
    private Long swipeTimeStamp;
    /**
     * 学生姓名
     */
    @TableField(exist=false )
    private String studentName;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 方向(默认 0:出站  1:进站)
     **/
    private int direction;
    /**
     * 消费类型
     **/
    private Integer amountType;

    public Long getSwipeTimeStamp() {
        return swipeTime == null ? swipeTimeStamp : swipeTime.getTime();
    }

    /**
     * 上链信息hash
     */
    private String txHash;

    /**
     * 上链回调唯一标识
     */
    private String txId;

    /**
     * 上链结果回调地址
     */
    @TableField(exist=false)
    private String callbackUrl;

    /**
     * 城市
     */
    @TableField(exist=false)
    private String region;
}
