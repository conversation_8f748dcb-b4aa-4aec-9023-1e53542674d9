package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @program: campus-bus-push
 * 班级信息
 * @author: zxr
 * @create: 2020-06-11 09:39
 **/
@Data
@TableName("T_CLASS")
@EqualsAndHashCode(callSuper = true)
public class Class extends BaseEntity{

    private static final long serialVersionUID = 3859459135965587147L;

    private Long id;
    private String className;
    private Integer schoolId;
    private String schoolName;
    private Integer gradeId;

}
