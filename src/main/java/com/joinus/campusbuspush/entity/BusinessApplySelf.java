package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * @program: campus-bus-push
 * 自推广业务开通记录表
 * @author: zxr
 * @create: 2020-06-10 15:25
 **/
@Data
@Accessors(chain = true)
@TableName("T_BUSINESS_APPLY_SELF")
@KeySequence("SEQ_T_BUSINESS_APPLY_SELF")
@EqualsAndHashCode(callSuper = true)
public class BusinessApplySelf extends Model {
    private static final long serialVersionUID = 8293166238231080460L;

    private Long id;
    /**
     * 关联产品表id
     */
    private String productId;
    /**
     * 关联子产品表id
     */
    private String subProductId;
    /**
     * 子业务产品现费用(分)
     */
    private int subProductFee;

    private String studentId;

    private String schoolId;

    private String gradeId;

    private String classId;
    /**
     * 家长手机号
     */
    private String phoneNum;
    /**
     * 手机号归属地
     */
    private String telSegment;
    /**
     * 操作类型
     */
    private Integer operType;
    /**
     * 操作时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date operTime;
    /**
     * 是否成功(1:成功; 2:失败)
     */
    private Integer res;
    /**
     * 备注
     */
    private String remark;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
