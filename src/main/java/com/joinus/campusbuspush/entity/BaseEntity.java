package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @program: campus-bus-push
 * 基础实体属性
 * @author: zxr
 * @create: 2020-05-11 15:56
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseEntity extends Model {

    private static final long serialVersionUID = -3403992161082464337L;

    /**
     * 逻辑删除字段
     */
    @TableLogic(value = "1", delval = "0")
    @TableField(value = "isactive",fill = FieldFill.INSERT)
    private Integer isActive;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
