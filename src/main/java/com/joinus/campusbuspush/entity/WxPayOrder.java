package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @program: campus-bus-push
 * 微信支付订单
 * @author: zxr
 * @create: 2020-05-21 10:28
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("T_BUS_CARD_PAY_ORDER")
@KeySequence("SEQ_T_BUS_CARD_PAY_ORDER")
public class WxPayOrder extends BaseEntity{

    private static final long serialVersionUID = -313089871971829307L;
    /**
     * ID
     */
    private Long id;
    /**
     * openId
     */
    @ApiModelProperty(value = "openId")
    private String openId;
    /**
     * 支付方式：1、微信支付，2、支付宝支付 3、系统赠送
     */
    @ApiModelProperty(value = "支付方式：1、微信支付，2、支付宝支付", example = "1")
    private Integer payTypeId;
    /**
     * 商品id，对应表T_PRODUCT_SELF_SUB的ID
     */
    @ApiModelProperty(value = "商品id", example = "385")
    private Long productId;
    /**
     * 公交卡业务表id 对应表T_BUS_CARD_BUSINESS的ID
     */
    @ApiModelProperty(value = "公交卡业务表id", example = "221")
    private Long businessId;
    /**
     * 订单金额(单位:分)
     */
    @ApiModelProperty(value = "订单金额(单位:分)", example = "1")
    private Long totalFee;
    /**
     * 学生ID
     */
    @ApiModelProperty(value = "学生ID", example = "3805027")
    private Long studentId;
    /**
     * 业务代码，0=退款，1=支付
     */
    private Integer serviceCode;
    /**
     * 订单描述
     */
    @ApiModelProperty(value = "订单描述，特定情况传交通卡号")
    private String summary;
    /**
     * 0：新建 1：预支付 2:预支付失败 3：支付成功 4：支付失败 8: 微信回调支付结果异常 9：异常订单 91:未收到预付单反馈 92:预付单通信失败 93:预付单反校验失败
     */
    @ApiModelProperty(value = "0：新建 1：预支付 2:预支付失败 3：支付成功 4：支付失败 8: 微信回调支付结果异常 9：异常订单 91:未收到预付单反馈 92:预付单通信失败 93:预付单反校验失败", example = "0")
    private Integer status;
    /**
     * 微信支付订单号
     */
    @ApiModelProperty(value = "微信支付订单号")
    private String transactionId;
    /**
     * 订单号，商户系统内部的订单号
     */
    @ApiModelProperty(value = "订单号，商户系统内部的订单号")
    private String orderNo;
    /**
     * 0:未处理 1:已预存 2:已开通 3:处理失败
     */
    @ApiModelProperty(value = "0:未处理 1:已预存 2:已开通 3:处理失败", example = "0")
    private Integer processStatus;
    /**
     * 支付成功时间
     */
    @ApiModelProperty(value = "支付成功时间")
    private Date payCompleteTime;
    /**
     * 交易类型：1、APP支付，2、扫码支付，3、H5支付
     */
    @TableField(exist = false)
    private Integer trade_type;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 优惠券ID
     **/
    @ApiModelProperty(value = "优惠券ID", example = "2")
    private Long couponId;

}
