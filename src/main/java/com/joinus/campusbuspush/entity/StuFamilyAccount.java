package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 学生家庭可信账户
 * @date 2021/6/28
 */
@Data
@TableName("T_BUS_STU_FAMILY_ACCOUNT")
@EqualsAndHashCode(callSuper = true)
public class StuFamilyAccount extends Model {
    private Long studentId;
    private String familyToken;
    private String publicKey;
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    private Date updateTime;
}
