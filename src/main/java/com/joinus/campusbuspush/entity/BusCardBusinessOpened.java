package com.joinus.campusbuspush.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @program: campus-bus-push
 * 公交卡已开通业务明细
 * @author: zxr
 * @create: 2020-08-19 13:49
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("T_BUS_CARD_BUSINESS_OPENED")
@KeySequence("SEQ_T_BUS_CARD_BUSINESS_OPENED")
@Accessors(chain = true)
public class BusCardBusinessOpened extends BaseEntity {

    private static final long serialVersionUID = -9210101054795976938L;

    private Long id;
    /**
     * 关联T_BUS_CARD_BUSINESS 表 id
     */
    private Long businessId;

    private Long studentId;
    /**
     * 关联T_PRODUCT_SELF 表id, 表示所属父级产品套餐
     */
    private Long parentProductId;
    /**
     * 关联 T_PRODUCT_SELF 表 id
     */
    private Long productId;
    /**
     * 关联 T_PRODUCT_SELF_SUB 表 id
     */
    private Long subProductId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 实际金额
     */
    private int actualAmount;
    /**
     * 业务开始时间
     */
    private Date startTime;
    /**
     * 业务结束时间
     */
    private Date endTime;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
