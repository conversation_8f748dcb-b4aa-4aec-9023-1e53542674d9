package com.joinus.campusbuspush.service;

import com.github.binarywang.wxpay.exception.WxPayException;
import com.joinus.campusbuspush.entity.pojo.response.WxPayOrderResponse;
import com.joinus.campusbuspush.entity.WxPayOrder;

import javax.servlet.http.HttpServletRequest;

/**
 * 微信支付订单service
 * @patams:
 * @return:
 * @Author: zxr
 * @Date: 2020/5/21 13:56
 */
public interface WxPayOrderService extends BaseService<WxPayOrder> {

    /**
     * 开业务微信下单
     * @patams: [wxPayOrder, request]
     * @return: com.joinus.campusbuspush.controller.response.WxPayOrderResponse
     * @Author: zxr
     * @Date: 2020/5/21 14:23
     */
    WxPayOrderResponse openBusiness(WxPayOrder wxPayOrder, HttpServletRequest request, boolean isGift);

    /**
     * 微信支付完成后的回调处理
     * @patams: [xmlData]
     * @return: boolean
     * @Author: zxr
     * @Date: 2020/5/21 15:57
     */
    boolean openBusinessNotify(String xmlData);

    /**
     * 根据订单号获取订单付款结果
     * @params: [orderNo]
     * @return: boolean
     * @Author: jxt
     * @Date: 2020/9/5 3:26 下午
     **/
    boolean getOrderResult(String orderNo);

    /**
     * giftOrder
     * @params [wxPayOrder, request]
     * @return boolean
     * <AUTHOR>
     * @date 2021/8/13 3:08 下午
     */
    WxPayOrderResponse giftOrder(WxPayOrder wxPayOrder, HttpServletRequest request);
}
