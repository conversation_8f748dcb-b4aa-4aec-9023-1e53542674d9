package com.joinus.campusbuspush.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.campusbuspush.entity.BusCardWhiteList;
import com.joinus.campusbuspush.entity.pojo.request.WhiteListParamRequest;

/**
 * 公交卡白名单服务层
 * @patams:
 * @return:
 * @Author: zxr
 * @Date: 2020/5/22 10:57
 */
public interface BusCardWhiteListService extends BaseService<BusCardWhiteList> {

    /**
     * 获取已开卡增量白名单
     * @patams: [request]
     * @return: com.baomidou.mybatisplus.core.metadata.IPage<com.joinus.campusbuspush.entity.BusCardWhiteList>
     * @Author: zxr
     * @Date: 2020/5/22 11:05
     */
    IPage<BusCardWhiteList> selectIncrementWhitePage(WhiteListParamRequest request);

    /**
     * 获取已开卡全量白名单
     * @patams: [request]
     * @return: com.baomidou.mybatisplus.core.metadata.IPage<com.joinus.campusbuspush.entity.BusCardWhiteList>
     * @Author: zxr
     * @Date: 2020/5/22 11:06
     */
    IPage<BusCardWhiteList> selectAllWhitePage(WhiteListParamRequest request);

    /**
     * 根据学生id更新白名单表
     * @patams: [phoneNo, businessId]
     * @return: void
     * @Author: zxr
     * @Date: 020/5/22 11:41
     */
    void updateWhiteListByBusinessId(String phoneNo, Long businessId);

    /**
     * 更新卡号白名单,如果表里不存在则插入
     * @patams: [phoneNo, busCardNo]
     * @return:
     * @Author: zxr
     * @Date: 2020/8/19 14:29
     */
    void updateWhiteList(String phoneNo, String busCardNo);


    /**
     * 未签到尝试推送白名单
     * @patams: [phoneNo, cardNo]
     * @return: java.lang.String
     * @Author: zxr
     * @Date: 2020/8/19 17:25
     */
    String pushWhiteList(String phoneNo, String logicCardNo);
}
