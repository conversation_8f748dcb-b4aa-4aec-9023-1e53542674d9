package com.joinus.campusbuspush.service;

import com.joinus.campusbuspush.entity.BusSwipeMessage;
import com.joinus.campusbuspush.entity.Student;

import java.util.List;

/**
 * <AUTHOR>
 * @description 区块链调用相关接口
 * @date 2021/6/25
 */
public interface BlockchainService {
    /**
     * createWallet
     * @params: [student]
     * @return: java.lang.String
     * @Author: jxt
     * @Date: 2021/6/25 1:59 下午
     */
    String createWallet(Student student);

    /**
     * queryByTxHash
     * @params: [studentId, txHash]
     * @return: com.joinus.campusbuspush.entity.BusSwipeMessage
     * @Author: jxt
     * @Date: 2021/6/28 4:29 下午
     */
    BusSwipeMessage queryByTxHash(Long studentId, String txHash);

    /**
     * queryByTxHash
     * @params: [studentId, txHashList]
     * @return: com.joinus.campusbuspush.entity.BusSwipeMessage
     * @Author: jxt
     * @Date: 2021/6/28 5:05 下午
     */
    List<BusSwipeMessage> queryByTxHashList(Long studentId, List<String> txHashList);
}
