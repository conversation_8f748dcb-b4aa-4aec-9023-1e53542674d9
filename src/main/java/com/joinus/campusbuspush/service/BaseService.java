package com.joinus.campusbuspush.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.campusbuspush.entity.pojo.request.PageParamRequest;

/**
 * @program: campus-bus-push
 * 基础service
 * @author: zxr
 * @create: 2020-05-13 14:11
 **/
public interface BaseService<T> extends IService<T> {
    /**
     * 创建分页对象
     *
     * @param pageParam 分页参数
     * @return page
     */
    default IPage<T> createPage(PageParamRequest pageParam) {
        return new Page<T>(pageParam.getCurrent(), pageParam.getSize());
    }
}
