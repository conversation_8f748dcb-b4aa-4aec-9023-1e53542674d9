package com.joinus.campusbuspush.service;

import com.joinus.campusbuspush.entity.BusSwipeMessage;
import com.joinus.campusbuspush.entity.dto.OpenedMessageParam;

import java.util.List;

/**
 * 刷卡消息服务层
 * @Author: zxr
 * @Date: 2020/5/15 11:20
 */
public interface BusSwipeMessageService extends BaseService<BusSwipeMessage> {

    /**
     * 保存刷卡信息
     * @param busSwipeMessage
     * @return
     */
    boolean saveSwipeMessage(BusSwipeMessage busSwipeMessage);

    /**
     * sendMessage
     * @param openedMessageParam 参数
     * @return void
     * <AUTHOR>
     * @date 2021/9/23 3:46 下午
     */
    void sendMessage(OpenedMessageParam openedMessageParam);

    /**
     * 按月合计查询历史刷卡信息（月票）
     * @patams: [studentId,openId]
     * @return: java.util.List<com.joinus.campusbuspush.entity.BusSwipeMessage>
     * @Author: zxr
     * @Date: 2020/5/20 14:15
     */
    List<BusSwipeMessage> getExpenseListSixMouth(Long studentId, String openId);

    /**
     *  根据卡号查询消费记录详情
     * @patams: [studentId,month,swipeType,openId]
     * @return: java.util.List<com.joinus.campusbuspush.entity.BusSwipeMessage>
     * @Author: zxr
     * @Date: 2020/5/20 15:01
     */
    List<BusSwipeMessage> getExpenseListDetail(Long studentId,String month,int swipeType, String openId);

    /**
     * sendNewSwipeMessage
     * @param busSwipeMessage 参数
     * @return void
     * <AUTHOR>
     * @date 2021/12/20 10:18 上午
     */
    void sendNewSwipeMessage(BusSwipeMessage busSwipeMessage);
}
