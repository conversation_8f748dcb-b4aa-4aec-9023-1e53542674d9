package com.joinus.campusbuspush.service;

import com.joinus.campusbuspush.entity.BusCardWxUser;
import com.joinus.campusbuspush.entity.WxUser;
import me.chanjar.weixin.mp.bean.result.WxMpUser;

/**
 * 公交卡微信用户相关服务层
 * @Author: zxr
 * @Date: 2020/5/15 11:20
 */
public interface BusCardWxUserService extends BaseService<BusCardWxUser> {

    /**
     * 用户关注
     * @patams: [openId, nickName]
     * @return: void
     * @Author: zxr
     * @Date: 2020/5/18 14:40
     */
    void subscribe(WxMpUser wxMpUser);

    /**
     * 用户取消关注
     * @patams: [openId]
     * @return: void
     * @Author: zxr
     * @Date: 2020/5/18 14:24
     */
    void unsubscribe(String openId);

    /**
     * 检查用户是否已关注
     * @patams: [openId]
     * @return: boolean
     * @Author: zxr
     * @Date: 2020/5/25 09:46
     */
    boolean checkSubscribeStatus(String openId);
    
    /**
     * 删除微信用户和学生关联信息
     * @params: [openId, studentId] 
     * @return: void
     * @Author: jxt
     * @Date: 2020/11/16 4:24 下午
     **/
    void delBusCardBussinessAndWxUserStudent(String openId, Long studentId);

    /**
     * queryBusCardWxUserByOpenId
     * @params: [openId]
     * @return: void
     * @Author: jxt
     * @Date: 2020/11/26 2:16 下午
     **/
    BusCardWxUser queryBusCardWxUserByOpenId(String openId);

    /**
     * queryWxUserByOpenId
     * @params [openId]
     * @return com.joinus.campusbuspush.entity.WxUser
     * <AUTHOR>
     * @date 2021/9/1 1:50 下午
     */
    WxUser queryWxUserByOpenId(String openId);

    /**
     * updateWxUserTelNum
     * @params [openId, phone]
     * @return void
     * <AUTHOR>
     * @date 2021/9/1 1:46 下午
     */
    void updateWxUserTelNum(String openId, String phone);
}
