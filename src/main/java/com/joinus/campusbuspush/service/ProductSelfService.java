package com.joinus.campusbuspush.service;

import com.joinus.campusbuspush.entity.ProductSelfSub;
import com.joinus.campusbuspush.entity.ProductSelfRatio;

import java.util.List;
import java.util.Map;

/**
 * 业务产品服务层
 * @Author: zxr
 * @Date: 2020/5/15 11:20
*/
public interface ProductSelfService extends BaseService<ProductSelfSub>{
    /**
     * 查询业务套餐列表
     * @patams: []
     * @return: java.util.List<Map<String,java.lang.Object>>
     * @Author: zxr
     * @Date: 2020/5/19 17:24
     */
    List<Map<String,Object>> selectProductList();

    /**
     * 查询套餐分成比例列表
     * @patams: [productId]
     * @return: java.util.List<com.joinus.campusbuspush.entity.ProductSelfRatio>
     * @Author: zxr
     * @Date: 2020/8/24 11:40
     */
    List<ProductSelfRatio> selectRatioProductList(Long productId);

    /**
     * 查询赠送套餐id
     * @return
     */
    Long selectGiftProductId();
}
