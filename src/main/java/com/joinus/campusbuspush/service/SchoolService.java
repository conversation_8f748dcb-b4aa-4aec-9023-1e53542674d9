package com.joinus.campusbuspush.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.campusbuspush.entity.pojo.request.SchoolParamRequest;
import com.joinus.campusbuspush.entity.School;

/**
 * 学校服务层
 * @Author: zxr
 * @Date: 2020/5/15 11:20
*/
public interface SchoolService extends BaseService<School>{

    /**
     * 根据名称,简拼,全拼模糊查询学校
     * @param request
     * @return
     */
    IPage<School> selectSchoolPage(SchoolParamRequest request);

    /**
     * 根据学生查询学校所在城市
     * @param studentId 学生id
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/12/23 10:04 上午
     */
    String getSchoolRegionByStudentId(Long studentId);
}
