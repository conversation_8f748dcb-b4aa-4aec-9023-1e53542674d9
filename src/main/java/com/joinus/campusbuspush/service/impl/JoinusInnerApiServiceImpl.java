package com.joinus.campusbuspush.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.google.gson.Gson;
import com.joinus.campusbuspush.common.enums.AppCodeEnum;
import com.joinus.campusbuspush.common.exception.exception.BaseException;
import com.joinus.campusbuspush.entity.pojo.SmsRequestBO;
import com.joinus.campusbuspush.service.JoinusInnerApiService;
import com.joinus.campusbuspush.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;

@Slf4j
@Service
public class JoinusInnerApiServiceImpl implements JoinusInnerApiService {

    @Resource
    private RedisUtil redisUtil;

    private String loginSmsRedisKey = "CMAPUS-BUS-PUSH:LOGIN:SMS:{}";

    @Value("${server.domain.qyl-gateway:https://gateway.joinuscn.com}")
    private String qylGatewayDomainName;

    @Value("${login.sms.not-send.mobile:13838380123}")
    private String loginSmsNotSendMobile;

    @Value("${login.sms.not-send.code:9671}")
    private String loginSmsNotSendCode;

    /**
     * 短信模板 【i家校】您的验证码是:{1},请于{2}内正确输入。请不要把验证码泄露给其他人。如非本人操作,可不用理会!
     */
    private String validateCodeTemplate = "898236";

    /**
     * 短信模板 【迅达安】您的验证码是:{1},请于{2}内正确输入。如非本人操作,可不用理会!
     */
    private String validateCodeXDATemplate = "635087";

    @Override
    public void sendLoginSms(String mobile,String ticket) throws Exception {
        if (ObjectUtil.isEmpty(ticket)) {
            throw new Exception("ticket，不允许为空!");
        }
        //校验5分钟内是否重复发送
        boolean existSms = redisUtil.hasKey(StrUtil.format(loginSmsRedisKey, mobile));
        if (existSms) {
            throw new Exception("验证码5分钟内有效，请勿重复请求");
        }
        try {
            //测试号码，用于小程序审核时使用
            if (mobile.equals(loginSmsNotSendMobile)) {
                return ;
            }
            boolean sendSuccess = sendSmsByAliSlideFromSmsCenter(mobile,ticket);
            if (sendSuccess) {
                redisUtil.set(StrUtil.format(loginSmsRedisKey, mobile), UUID.randomUUID().toString(), 5 * 60);
            }
        } catch (Exception e) {
            if (e instanceof BaseException) {
                JSONObject jsonObject = new JSONObject(e.getMessage());
                String msg = jsonObject.getStr("msg");
                throw new Exception(msg);
            } else {
                e.printStackTrace();
                throw new Exception("验证码发送失败");
            }
        }
    }

    @Override
    public boolean verifyLoginSms(String mobile, String smsCode) throws Exception {
        //测试号码+测试验证码，用于小程序审核时使用
        if (mobile.equals(loginSmsNotSendMobile) && smsCode.equals(loginSmsNotSendCode)) {
            return true;
        }
        return verifySmsCodeFromSmsCenter(mobile, smsCode);
    }

    private boolean sendSmsByAliSlideFromSmsCenter(String mobile,String ticket) throws Exception {
        String templateId = validateCodeTemplate;
        String appCode = AppCodeEnum.YX_MINI.getAppCode();
        templateId = validateCodeXDATemplate;
        appCode = AppCodeEnum.XDA_MIMI.getAppCode();
        SmsRequestBO requestBody = new SmsRequestBO(appCode, mobile, 1, templateId, "####,5分钟", 1,ticket);
        String json = new Gson().toJson(requestBody);
        String url = qylGatewayDomainName + "/sms/sendYzmSms";
        try {
            HttpResponse response = HttpUtil.createPost(url)
                    .body(json)
                    .timeout(10 * 1000)
                    .execute();
            if (response.isOk()) {
                JSONObject jsonObject = new JSONObject(response.body());
                String code = jsonObject.getStr("code");
                String data = jsonObject.getStr("data");
                if ("200".equals(code)) {
                    return true;
                }
            }
            log.error("发送短信失败：" + response.body());
            throw new BaseException(300, response.body());
        } catch (Exception e) {
            if (e instanceof BaseException) {
                throw e;
            } else {
                throw new Exception("发送短信时发生错误：" + e.getMessage());
            }
        }
    }

    private boolean verifySmsCodeFromSmsCenter(String phoneNumber, String verificationCode) throws Exception {
        String appCode = AppCodeEnum.YX_MINI.getAppCode();
        appCode = AppCodeEnum.XDA_MIMI.getAppCode();
        SmsRequestBO requestBody = new SmsRequestBO(appCode, phoneNumber, 1, verificationCode);
        String json = new Gson().toJson(requestBody);
        String url = qylGatewayDomainName + "/sms/verifyYzm";
        try {
            log.info("校验验证码请求：" + json);
            HttpResponse response = HttpUtil.createPost(url)
                    .body(json)
                    .timeout(10 * 1000)
                    .execute();
            if (response.isOk()) {
                JSONObject jsonObject = new JSONObject(response.body());
                String code = jsonObject.getStr("code");
                String data = jsonObject.getStr("data");
                if ("200".equals(code) && "true".equals(data)) {
                    return true;
                } else {
                    log.info("验证码校验失败：" + response.body());
                    return false;
                }
            }
        } catch (Exception e) {
            throw new Exception("验证码校验时发生错误：" + e.getMessage());
        }
        return false;
    }

}
