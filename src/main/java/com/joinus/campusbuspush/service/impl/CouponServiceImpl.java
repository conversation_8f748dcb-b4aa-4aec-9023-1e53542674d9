package com.joinus.campusbuspush.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.campusbuspush.common.GlobalConstants;
import com.joinus.campusbuspush.common.exception.constant.enums.CommonResponseEnum;
import com.joinus.campusbuspush.entity.Coupon;
import com.joinus.campusbuspush.entity.pojo.request.CouponParamRequest;
import com.joinus.campusbuspush.entity.pojo.response.CouponResponse;
import com.joinus.campusbuspush.mapper.BusCardBusinessCouponMapper;
import com.joinus.campusbuspush.service.CouponService;
import com.joinus.campusbuspush.util.CommUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-17
 */
@Service
@AllArgsConstructor
public class CouponServiceImpl extends BaseServiceImpl<BusCardBusinessCouponMapper, Coupon> implements CouponService {

    private BusCardBusinessCouponMapper couponMapper;

    @Override
    public void exchangeCoupon(String openId, String couponCode) {
        QueryWrapper<Coupon> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("COUPON_CODE", couponCode.toUpperCase());
        Coupon coupon = couponMapper.selectOne(queryWrapper);
        // 判断优惠码是否存在，兑换过或者已过兑换期
        CommonResponseEnum.NOT_FOUND.assertIsFalse(coupon == null, "兑换码不存在");
        CommonResponseEnum.VALID_ERROR.assertIsFalse(coupon.getCouponStatus() != GlobalConstants.COUPON_STATUS.UNEXCHANGE, "兑换码已使用");
        Date currentTime = new Date();
        CommonResponseEnum.VALID_ERROR.assertIsFalse(DateUtil.compare(currentTime, coupon.getExchangeStartTime()) < 0 || DateUtil.compare(currentTime, coupon.getExchangeEndTime()) > 0, "不在兑换期内");
        // 判断这个此人是否存在其他可用的兑换券
        CommonResponseEnum.VALID_ERROR.assertIsFalse(this.getCouponByOpenId(openId) != null, "存在未使用的优惠券");
        coupon.setOpenId(openId);
        coupon.setCouponStatus(GlobalConstants.COUPON_STATUS.EXCHANGED);
        couponMapper.updateById(coupon);
    }

    @Override
    public Coupon getCouponByOpenId(String openId) {
        QueryWrapper<Coupon> wrapper = new QueryWrapper<>();
        wrapper.eq("OPEN_ID", openId);
        wrapper.eq("COUPON_STATUS", GlobalConstants.COUPON_STATUS.EXCHANGED);
        wrapper.ge("USE_END_TIME", new Date());
        List<Coupon> coupons = couponMapper.selectList(wrapper);
        if (coupons.size() > 0 ){
            return coupons.get(0);
        }
        return null;
    }

    @Override
    public IPage<CouponResponse> listCoupon(CouponParamRequest request) {
        IPage<CouponResponse> page = new Page<>(request.getCurrent(), request.getSize());
        return page.setRecords(couponMapper.listCoupon(page, request));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void genrateCoupons(CouponParamRequest request) {
        List<Coupon> coupons = new ArrayList<>();
        for (int i = 0; i < request.getSize(); i++) {
            Coupon coupon = Coupon.builder()
                    .couponCode(CommUtil.generateCouponCode())
                    .couponAmount(request.getCouponAmount())
                    .createTime(new Date())
                    .exchangeStartTime(DateUtil.parse(request.getExchangeDateBegin()))
                    .exchangeEndTime(DateUtil.parse(request.getExchangeDateEnd()+" 23:59:59"))
                    .useStartTime(DateUtil.parse(request.getUseDateBegin()))
                    .useEndTime(DateUtil.parse(request.getUseDateEnd()+" 23:59:59"))
                    .couponStatus(GlobalConstants.COUPON_STATUS.UNEXCHANGE)
                    .build();
            coupons.add(coupon);
        }
        this.saveBatch(coupons);
    }
}
