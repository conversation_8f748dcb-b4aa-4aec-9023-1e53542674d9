package com.joinus.campusbuspush.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.campusbuspush.entity.pojo.request.SchoolParamRequest;
import com.joinus.campusbuspush.entity.School;
import com.joinus.campusbuspush.mapper.SchoolMapper;
import com.joinus.campusbuspush.service.SchoolService;
import org.springframework.stereotype.Service;

/**
 * @program: campus-bus-push
 * 学校相关服务实现类
 * @author: zxr
 * @create: 2020-05-15 13:51
 **/
@Service
public class SchoolServiceImpl extends BaseServiceImpl<SchoolMapper, School> implements SchoolService {

    @Override
    public IPage<School> selectSchoolPage(SchoolParamRequest request) {
        return this.lambdaQuery().select(School::getId, School::getSchoolName)
                .like(School::getSchoolName, request.getSchoolName())
                .or().like(School::getSchoolNameJp, request.getSchoolName())
                .or().like(School::getSchoolNameQp, request.getSchoolName())
                .page(createPage(request));
    }

    @Override
    public String getSchoolRegionByStudentId(Long studentId) {
        return this.baseMapper.getSchoolRegionByStudentId(studentId);
    }

}
