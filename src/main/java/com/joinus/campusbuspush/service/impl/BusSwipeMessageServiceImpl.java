package com.joinus.campusbuspush.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.campusbuspush.common.GlobalConstants;
import com.joinus.campusbuspush.common.exception.constant.enums.CommonResponseEnum;
import com.joinus.campusbuspush.common.exception.exception.BaseException;
import com.joinus.campusbuspush.kafka.KafkaProducer;
import com.joinus.campusbuspush.entity.BusCardBusiness;
import com.joinus.campusbuspush.entity.BusSwipeMessage;
import com.joinus.campusbuspush.entity.dto.OpenedMessageParam;
import com.joinus.campusbuspush.mapper.BusCardBusinessMapper;
import com.joinus.campusbuspush.mapper.BusSwipeMessageMapper;
import com.joinus.campusbuspush.service.BlockchainService;
import com.joinus.campusbuspush.service.BusSwipeMessageService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: campus-bus-push
 * 刷卡消息实现类
 * @author: zxr
 * @create: 2020-05-16 20:51
 **/
@Service
public class BusSwipeMessageServiceImpl extends BaseServiceImpl<BusSwipeMessageMapper, BusSwipeMessage> implements BusSwipeMessageService {
    @Resource
    private KafkaProducer kafkaProducer;
    @Resource
    private BusSwipeMessageMapper busSwipeMessageMapper;
    @Resource
    private BlockchainService blockchainService;
    @Resource
    private BusCardBusinessMapper busCardBusinessMapper;
    @Value("${transaction.host}")
    private String transactionHost;
    @Value("${sendTransaction.api}")
    private String sendTransactionApi;
    @Value("${transaction.callback.url}")
    private String transactionCallbackUrl;

    @Override
    public boolean saveSwipeMessage(BusSwipeMessage busSwipeMessage) {
        String txId = this.sendTransaction(busSwipeMessage);
        busSwipeMessage.setTxId(txId);
        BusSwipeMessage swipeMessage = new BusSwipeMessage();
        swipeMessage.setStudentId(busSwipeMessage.getStudentId());
        swipeMessage.setSwipeType(busSwipeMessage.getSwipeType());
        swipeMessage.setSwipeTime(new Date(busSwipeMessage.getSwipeTimeStamp()));
        swipeMessage.setCardNo(busSwipeMessage.getCardNo());
        swipeMessage.setTxId(txId);
        return this.save(swipeMessage);

    }

    @Override
    public void sendMessage(OpenedMessageParam openedMessageParam) {
        kafkaProducer.sendMessage(JSONUtil.toJsonStr(openedMessageParam));
    }

    @Override
    public List<BusSwipeMessage> getExpenseListSixMouth(Long studentId, String openId){
        BusCardBusiness business = busCardBusinessMapper.selectByStudentIdAndOpenId(studentId, openId);
        boolean busBizValid = business.getBusEndTime() != null && new Date().compareTo(business.getBusEndTime()) < 0;
        boolean subwayBizValid = business.getSubwayEndTime() != null && new Date().compareTo(business.getSubwayEndTime()) < 0;
        if (busBizValid || subwayBizValid) {
            return busSwipeMessageMapper.getExpenseListSixMouth(studentId);
        }else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<BusSwipeMessage> getExpenseListDetail(Long studentId, String month, int swipeType, String openId) {
        BusCardBusiness business = busCardBusinessMapper.selectByStudentIdAndOpenId(studentId, openId);
        boolean busBizValid = business.getBusEndTime() != null && new Date().compareTo(business.getBusEndTime()) < 0;
        boolean subwayBizValid = business.getSubwayEndTime() != null && new Date().compareTo(business.getSubwayEndTime()) < 0;
        if (swipeType == 0 && !busBizValid) {
            return new ArrayList<>();
        }
        if (swipeType == 1 && !subwayBizValid) {
            return new ArrayList<>();
        }
        if (swipeType == 99) {
            if (!busBizValid && !subwayBizValid) {
                return new ArrayList<>();
            }
            if (!busBizValid && subwayBizValid) {
                swipeType = 1;
            }
            if (busBizValid && !subwayBizValid) {
                swipeType = 0;
            }
        }
        List<BusSwipeMessage> busSwipeMessageList = busSwipeMessageMapper.getExpenseListDetail(studentId,month,swipeType);
        List<BusSwipeMessage> blockchainMessageList = blockchainService.queryByTxHashList(studentId, busSwipeMessageList.stream().map(BusSwipeMessage::getTxHash).filter(Objects::nonNull).collect(Collectors.toList()));
        blockchainMessageList.forEach(busSwipeMessage -> {
            busSwipeMessage.setSwipeTime(new Date(busSwipeMessage.getSwipeTimeStamp()));
            busSwipeMessage.setBusLineNo(busSwipeMessage.getBusLineNo()+"("+busSwipeMessage.getBusStation()+")");
            if (busSwipeMessage.getAmountType() == GlobalConstants.AMOUNT_TYPE_WALLET){
                busSwipeMessage.setAmount(busSwipeMessage.getAmount() * 100);
            }else {
                busSwipeMessage.setAmount(0D);
            }
        });
        return blockchainMessageList;
    }

    /**
     * sendTransaction
     * @params: [busSwipeMessage]
     * @return: java.lang.String
     * @Author: jxt
     * @Date: 2021/6/28 3:33 下午
     */
    private String sendTransaction(BusSwipeMessage busSwipeMessage){
        busSwipeMessage.setCallbackUrl(transactionCallbackUrl);
        String result = HttpRequest.post(transactionHost + sendTransactionApi)
                .body(JSONUtil.toJsonStr(busSwipeMessage), ContentType.JSON.getValue())
                .execute()
                .body();
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsFalse(ObjectUtil.isEmpty(result));
        JSONObject resultJson = JSONUtil.parseObj(result);
        if (!CommonResponseEnum.SUCCESS.getMessage().equals(resultJson.getStr("status"))){
            throw new BaseException(resultJson.getInt("code"),resultJson.getStr("msg"));
        }
        String cid = resultJson.getStr("data");
        return cid;
    }

    /**
     * sendNewSwipeMessage
     * @param busSwipeMessage 参数
     * @return void
     * <AUTHOR>
     * @date 2021/12/20 10:18 上午
     */
    @Override
    public void sendNewSwipeMessage(BusSwipeMessage busSwipeMessage) {
        kafkaProducer.sendNewSwipeMessage(JSONUtil.toJsonStr(busSwipeMessage));
    }
}
