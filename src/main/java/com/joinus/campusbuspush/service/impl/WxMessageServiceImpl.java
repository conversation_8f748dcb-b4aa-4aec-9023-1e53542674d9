package com.joinus.campusbuspush.service.impl;

import cn.hutool.core.util.StrUtil;
import com.joinus.campusbuspush.entity.dto.OpenedMessageParam;
import com.joinus.campusbuspush.service.WxMessageService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpTemplateMsgServiceImpl;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 微信消息发送实现
 * @Author: zxr
 * @Date: 2020/5/14 21:20
 */
@Slf4j
@AllArgsConstructor
@Service("wxMessageService")
public class WxMessageServiceImpl implements WxMessageService {

    private WxMpService wxMpService;

    @Override
    public void sendMessage(OpenedMessageParam messageParam) {
        try {
            WxMpTemplateMsgServiceImpl wxMpTemplateMsgService = new WxMpTemplateMsgServiceImpl(wxMpService);
            WxMpTemplateMessage wxMpTemplateMessage = new WxMpTemplateMessage();
            wxMpTemplateMessage.setToUser(messageParam.getOpenId());
            wxMpTemplateMessage.setTemplateId(messageParam.getTemplateId());
            wxMpTemplateMessage.setUrl(messageParam.getUrl());

            List<WxMpTemplateData> templateData = new ArrayList<>();
            templateData.add(new WxMpTemplateData("first", messageParam.getFirst()));

            if (StrUtil.isNotEmpty(messageParam.getKeyword1())) {
                WxMpTemplateData wxMpTemplateData;
                if (StrUtil.isNotEmpty(messageParam.getColorKeyword1())){
                    wxMpTemplateData = new WxMpTemplateData("keyword1", messageParam.getKeyword1(), messageParam.getColorKeyword1());
                }else {
                    wxMpTemplateData = new WxMpTemplateData("keyword1", messageParam.getKeyword1());
                }
                templateData.add(wxMpTemplateData);
            }
            if (StrUtil.isNotEmpty(messageParam.getKeyword2())) {
                WxMpTemplateData wxMpTemplateData;
                if (StrUtil.isNotEmpty(messageParam.getColorKeyword2())){
                    wxMpTemplateData = new WxMpTemplateData("keyword2", messageParam.getKeyword2(), messageParam.getColorKeyword2());
                }else {
                    wxMpTemplateData = new WxMpTemplateData("keyword2", messageParam.getKeyword2());
                }
                templateData.add(wxMpTemplateData);
            }
            if (StrUtil.isNotEmpty(messageParam.getKeyword3())) {
                WxMpTemplateData wxMpTemplateData;
                if (StrUtil.isNotEmpty(messageParam.getColorKeyword2())){
                    wxMpTemplateData = new WxMpTemplateData("keyword3", messageParam.getKeyword3(), messageParam.getColorKeyword3());
                }else {
                    wxMpTemplateData = new WxMpTemplateData("keyword3", messageParam.getKeyword3());
                }
                templateData.add(wxMpTemplateData);
            }
            if (StrUtil.isNotEmpty(messageParam.getKeyword4())) {
                WxMpTemplateData wxMpTemplateData;
                if (StrUtil.isNotEmpty(messageParam.getColorKeyword2())){
                    wxMpTemplateData = new WxMpTemplateData("keyword4", messageParam.getKeyword4(), messageParam.getColorKeyword4());
                }else {
                    wxMpTemplateData = new WxMpTemplateData("keyword4", messageParam.getKeyword4());
                }
                templateData.add(wxMpTemplateData);
            }
            if (StrUtil.isNotEmpty(messageParam.getKeyword5())) {
                templateData.add(new WxMpTemplateData("keyword5", messageParam.getKeyword5()));
            }
            if(StrUtil.isNotEmpty(messageParam.getRemark())){
                templateData.add(new WxMpTemplateData("remark", messageParam.getRemark(),messageParam.getColorRemark()));
            }
            wxMpTemplateMessage.setData(templateData);
            log.warn("wxMpTemplateMessage:{}",wxMpTemplateMessage.toString());
            wxMpTemplateMsgService.sendTemplateMsg(wxMpTemplateMessage);
        } catch (WxErrorException e) {
            log.error("微信消息发送错误,message:{},Exception:{}",e.getMessage(), e);
        }
    }


}
