package com.joinus.campusbuspush.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.joinus.campusbuspush.common.GlobalConstants;
import com.joinus.campusbuspush.entity.BusCard;
import com.joinus.campusbuspush.entity.BusCardBusiness;
import com.joinus.campusbuspush.entity.BusCardBusinessBinding;
import com.joinus.campusbuspush.entity.pojo.request.OpenBusinessParamRequest;
import com.joinus.campusbuspush.mapper.BusCardBusinessBindingMapper;
import com.joinus.campusbuspush.mapper.BusCardBusinessMapper;
import com.joinus.campusbuspush.mapper.BusCardMapper;
import com.joinus.campusbuspush.service.BusCardService;
import com.joinus.campusbuspush.service.BusCardWhiteListService;
import com.joinus.campusbuspush.util.CommUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @program: campus-bus-push
 * 公交卡相关服务类
 * @author: zxr
 * @create: 2020-08-19 14:02
 **/
@Service
@AllArgsConstructor
public class BusCardServiceImpl extends BaseServiceImpl<BusCardMapper, BusCard> implements BusCardService {

    private BusCardWhiteListService busCardWhiteListService;

    private BusCardMapper busCardMapper;
    private BusCardBusinessMapper busCardBusinessMapper;
    private BusCardBusinessBindingMapper busCardBusinessBindingMapper;
    private static ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat("pushWhiteList-pool-%d").build();
    private static ThreadPoolExecutor executor = new ThreadPoolExecutor(10, 10, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), namedThreadFactory);

    @Override
    public List<Long> getCardIdListByBusinessId(Long businessId) {
        QueryWrapper<BusCardBusinessBinding> bindingQueryWrapper = new QueryWrapper<>();
        bindingQueryWrapper.eq("business_id", businessId);
        List<BusCardBusinessBinding> list = busCardBusinessBindingMapper.selectList(bindingQueryWrapper);
        return list.stream().map(BusCardBusinessBinding::getCardId).collect(Collectors.toList());
    }

    @Override
    public List<BusCard> getCardListByStudentIdAndOpenId(Long studentId, String openId) {
        return busCardMapper.getBusCardListByStudentIdAndOpenId(studentId, openId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int addBusCardBinding(Long studentId, String busCardNo, String openId) {
        QueryWrapper<BusCardBusiness> businessQueryWrapper = new QueryWrapper<>();
        businessQueryWrapper.eq("student_id", studentId);
        businessQueryWrapper.eq("open_id", openId);
        BusCardBusiness busCardBusiness = busCardBusinessMapper.selectOne(businessQueryWrapper);
        return saveBusCardBinding(busCardBusiness, busCardNo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveBusCardBinding(BusCardBusiness busCardBusiness, String busCardNo) {
        //查询卡是否存在，若不存在则新增，若存在，则判断绑定关系
        QueryWrapper<BusCard> busCardQueryWrapper = new QueryWrapper<>();
        busCardQueryWrapper.eq("card_no", busCardNo);
        BusCard busCard = busCardMapper.selectOne(busCardQueryWrapper);
        if (ObjectUtil.isNull(busCard)) {
            //卡不存在
            String logicCardNo = CommUtil.generateLogicCardNo(busCardNo);
            busCard = new BusCard();
            busCard.setCardNo(busCardNo)
                    .setCreateTime(new Date())
                    .setLogicCardNo(logicCardNo)
                    .setUpdateTime(new Date());
            busCardMapper.insert(busCard);
        }
        List<Long> busCardIdList = getCardIdListByBusinessId(busCardBusiness.getId());
        Long busCardId = busCard.getId();
        if (null != busCardIdList && !busCardIdList.contains(busCardId)) {
            cardActiveAndSave(busCardBusiness, busCardId, busCard.getLogicCardNo());
        }
        return enableBusCard(busCardBusiness.getStudentId(), busCardNo, busCardBusiness.getOpenId());
    }

    @Override
    public int enableBusCard(Long studentId, String busCardNo, String openId) {
        BusCardBusiness business = busCardBusinessMapper.selectByStudentIdAndOpenId(studentId, openId);
        BusCard busCard = getBusCardByCardNo(busCardNo);
        QueryWrapper<BusCardBusinessBinding> bindingQueryWrapper = new QueryWrapper<>();
        bindingQueryWrapper.eq("business_id", business.getId());
        bindingQueryWrapper.eq("card_id", busCard.getId());
        BusCardBusinessBinding binding = busCardBusinessBindingMapper.selectOne(bindingQueryWrapper);
        binding.setUseStatus(GlobalConstants.BUS_CARD_ENABLE);
        UpdateWrapper<BusCardBusinessBinding> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("business_id", binding.getBusinessId());
        updateWrapper.eq("card_id", binding.getCardId());
        return busCardBusinessBindingMapper.update(binding, updateWrapper);
    }

    /**
     * getBusCardByCardNo
     * @params [cardNo]
     * @return com.joinus.campusbuspush.entity.BusCard
     * <AUTHOR>
     * @date 2021/8/17 2:30 下午
     */
    private BusCard getBusCardByCardNo(String cardNo) {
        QueryWrapper<BusCard> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("card_no", cardNo);
        return getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    public int disableBusCard(OpenBusinessParamRequest request) {
        BusCardBusinessBinding binding = busCardBusinessBindingMapper.selectBindingByOpenIdAndCardNo(request.getOpenId(), request.getCardNo());
        UpdateWrapper<BusCardBusinessBinding> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("business_id", binding.getBusinessId());
        updateWrapper.eq("card_id", binding.getCardId());
        binding.setUseStatus(GlobalConstants.BUS_CARD_DISABLE);
        return busCardBusinessBindingMapper.update(binding, updateWrapper);
    }

    @Override
    public int cancelBasCard(OpenBusinessParamRequest request) {
        BusCardBusinessBinding binding = busCardBusinessBindingMapper.selectBindingByOpenIdAndCardNo(request.getOpenId(), request.getCardNo());
        //删除绑定关系
        QueryWrapper<BusCardBusinessBinding> delWrapper = new QueryWrapper<>();
        delWrapper.eq("business_id", binding.getBusinessId());
        delWrapper.eq("card_id", binding.getCardId());
        return busCardBusinessBindingMapper.delete(delWrapper);
    }

    @Override
    public BusCard queryByLogicCardCode(String logicCardCode) {
        QueryWrapper<BusCard> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("logic_card_no", logicCardCode);
        return busCardMapper.selectOne(queryWrapper);
    }

    /**
     * 公交卡激活并保存业务关联关系
     *
     * @patams: [busCardBusiness, cardNo, busCardId]
     * @return: void
     * @Author: zxr
     * @Date: 2020/8/19 14:37
     */
    private void cardActiveAndSave(BusCardBusiness busCardBusiness, Long busCardId, String logicCardNo) {
        BusCardBusinessBinding busCardBusinessBinding = new BusCardBusinessBinding();
        busCardBusinessBinding.setBusinessId(busCardBusiness.getId());
        busCardBusinessBinding.setCardId(busCardId);
        busCardBusinessBinding.setUseStatus(GlobalConstants.BUS_CARD_ENABLE);
        busCardBusinessBinding.insert();
        busCardWhiteListService.updateWhiteList(busCardBusiness.getPhoneNo(), logicCardNo);
        pushWhiteList(logicCardNo, busCardBusiness.getPhoneNo());
    }

    /**
     * 异步推送白名单
     *
     * @param logicCardNo
     * @param phoneNo
     */
    @Override
    public void pushWhiteList(String logicCardNo, String phoneNo) {
        Callable<String> newPushWhiteListCall = () -> busCardWhiteListService.pushWhiteList(phoneNo, logicCardNo);
        FutureTask<String> task = new FutureTask<>(newPushWhiteListCall);
        executor.submit(task);
    }

}
