package com.joinus.campusbuspush.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.joinus.campusbuspush.common.GlobalConstants;
import com.joinus.campusbuspush.common.exception.constant.enums.CommonResponseEnum;
import com.joinus.campusbuspush.entity.*;
import com.joinus.campusbuspush.entity.pojo.request.OpenBusinessParamRequest;
import com.joinus.campusbuspush.entity.pojo.request.YikatongParamRequest;
import com.joinus.campusbuspush.entity.pojo.response.BusCardBusinessResponse;
import com.joinus.campusbuspush.entity.pojo.response.OpenBusinessResponse;
import com.joinus.campusbuspush.entity.pojo.response.YikatongParamResponse;
import com.joinus.campusbuspush.mapper.*;
import com.joinus.campusbuspush.service.BlockchainService;
import com.joinus.campusbuspush.service.BusCarBusinessService;
import com.joinus.campusbuspush.service.BusCardService;
import com.joinus.campusbuspush.service.BusCardWxUserService;
import com.joinus.campusbuspush.util.CommUtil;
import com.joinus.campusbuspush.util.MapperUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @program: campus-bus-push
 * 公交卡业务实现类
 * @author: zxr
 * @create: 2020-05-16 20:51
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class BusCarBusinessServiceImpl extends BaseServiceImpl<BusCardBusinessMapper, BusCardBusiness> implements BusCarBusinessService {

    private final BusCardMapper busCardMapper;
    private final BusCardBusinessMapper busCardBusinessMapper;
    private final StudentMapper studentMapper;
    private final ParentMapper parentMapper;
    private final BusCardService busCardService;
    private final BusCardWxUserService busCardWxUserService;
    private final BusCardBusinessOpenedMapper businessOpenedMapper;
    private final BlockchainService blockchainService;
    @Value("${yikatong.valid.url}")
    private String yktValidUrl;
    @Value("${user.protocol.content}")
    private String userProtocol;

    @Override
    public List<BusCardBusiness> selectBusCardBusiness(String cardNo, int swipeType) {
        return busCardBusinessMapper.selectBusCardBusiness(cardNo, swipeType);
    }

    @Override
    public List<BusCardBusinessResponse> selectStudentList(String openId) {
        List<BusCardBusiness> busCardBusinessList = busCardBusinessMapper.selectBusCardStudent(openId);
        for (BusCardBusiness busCardBusiness : busCardBusinessList) {
            List<BusCard> cards = busCardMapper.getUseingBusCardByStudentIdAndOpenId(busCardBusiness.getStudentId(), openId);
            busCardBusiness.setCardNoArr(cards.stream().map(BusCard::getCardNo).toArray(String[]::new));
        }
        return MapperUtils.mapList(busCardBusinessList, BusCardBusinessResponse.class);
    }

    @Override
    public String checkBusCard(OpenBusinessParamRequest request) {
        YikatongParamRequest yktRequest = YikatongParamRequest.builder()
                .cardNo(request.getCardNo().trim())
                .name(request.getStudentName())
                .protocol(userProtocol)
                .cardType(request.getCardType())
                .identity("0")
                .parentName("张三")
                .parentIdentity("1")
                .phoneNo(request.getPhoneNo())
                .timestamp(System.currentTimeMillis())
                .build();
        long startTimestamp = System.currentTimeMillis();
        log.info("一卡通校验参数:{}", JSONUtil.toJsonStr(yktRequest));
        String result = HttpRequest.post(yktValidUrl).body(JSONUtil.toJsonStr(yktRequest), ContentType.JSON.getValue()).execute().body();
        log.info("一卡通校验结果:{},交通卡号:{},耗时:{}ms",result,request.getCardNo(),System.currentTimeMillis() - startTimestamp);
        YikatongParamResponse response = JSONUtil.toBean(result, YikatongParamResponse.class);
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(response.getStatus() == GlobalConstants.YIKATONG_VALID_SUCCESS_CODE, "信息验证失败，请查看你输入的信息是否有误，如有问题请联系客服967111");
        return null;
    }

    @Transactional
    @Override
    public OpenBusinessResponse saveBusinessInfo(OpenBusinessParamRequest request) {
        //验证微信用户是否存在
        CommonResponseEnum.VALID_ERROR.assertIsTrue(busCardWxUserService.checkSubscribeStatus(request.getOpenId()), "您未关注公众号，绑卡信息保存失败");
        busCardWxUserService.updateWxUserTelNum(request.getOpenId(), request.getPhoneNo());

        //保存学生/长辈
        Student student = saveStudent(request);
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(ObjectUtil.isNotEmpty(student), "学生信息保存失败");

        //保存家长/儿女
        Parent parent = saveParent(request);
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(ObjectUtil.isNotEmpty(parent), "家长信息保存失败");

        //保存学生/长辈和家长/儿女关系
        int saveStudentParentResult = saveStudentParent(student, parent, request.getChildRelation());
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(saveStudentParentResult > 0, "家长和学生关系保存失败");

        //保存公交卡业务表
        BusCardBusiness busCardBusiness = saveBusCardBusiness(request, student.getId());
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(ObjectUtil.isNotEmpty(busCardBusiness), "用户绑卡信息保存失败");

        //保存公交卡和业务关系
        int updateBusCard = busCardService.saveBusCardBinding(busCardBusiness, request.getCardNo().trim());
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsFalse(updateBusCard == 0, "公交卡信息更新失败");

        //创建区块链家庭可信账户
        blockchainService.createWallet(student);

        //查询是否赠送优惠券
        int productGifted = busCardBusiness.getProductGifted();
        return new OpenBusinessResponse(student.getId(), busCardBusiness.getId(), CommUtil.generateLogicCardNo(request.getCardNo().trim()), productGifted);
    }

    @Override
    public List<BusCardBusiness> selectExpiringBusiness() {
        return busCardBusinessMapper.selectExpiringBusiness();
    }

    @Override
    public Date getOpenedLastEndTime(Long studentId, Long productId){
        Date lastEndTime = businessOpenedMapper.getOpenedLastEndTime(studentId,productId);
        return ObjectUtil.isNotNull(lastEndTime) ? lastEndTime : DateUtil.date();
    }

    @Override
    public BusCardBusiness selectByStudentIdAndOpenId(Long studentId, String openId) {
        return busCardBusinessMapper.selectByStudentIdAndOpenId(studentId, openId);
    }

    /**
     * 保存学生信息
     * @patams: [request]
     * @return: com.joinus.campusbuspush.entity.Student
     * @Author: zxr
     * @Date: 2020/5/25 14:20
     */
    private Student saveStudent(OpenBusinessParamRequest request) {
        Student student = studentMapper.checkStudentExistsByCardNo(request.getCardNo());
        //学生如果不存在就新建
        if (ObjectUtil.isEmpty(student)) {
            student = new Student();
            student.setStudentName(request.getStudentName());
            student.setSchoolId(request.getSchoolId());
            student.setClassId(request.getClassId());
            student.setIsBus(0);
            student.setIsDorm(2);
            student.setIsActive(1);
            student.insert();
        }else {
            //存在就更新名字亲情号等信息
            student.setStudentName(request.getStudentName());
            student.setIsActive(1);
            student.updateById();
        }
        return student;
    }

    /**
     * 保存家长信息
     * @patams: [request]
     * @return: com.joinus.campusbuspush.entity.Parent
     * @Author: zxr
     * @Date: 2020/5/28 15:28
     */
    private Parent saveParent(OpenBusinessParamRequest request) {
        QueryWrapper<Parent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tel_num", request.getPhoneNo());
        Parent parent = parentMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(parent)) {
            parent = new Parent();
            parent.setAddTime(DateUtil.date());
            parent.setTelNum(request.getPhoneNo());
            parent.setPassword(SecureUtil.md5(request.getPhoneNo().substring(5, 11)));
            parentMapper.insert(parent);
        }
        return parent;

    }

    /**
     * 保存学生和家长关系表
     * @patams: [student, parent]
     * @return: java.lang.Integer
     * @Author: zxr
     * @Date: 2020/5/28 15:32
     */
    private int saveStudentParent(Student student, Parent parent, String childRelation) {
        int result = studentMapper.selectStudentParentExist(student.getId(), parent.getId());
        if (result <= 0) {
            Integer maxSort = studentMapper.selectStudentParentMaxSort(student.getId());
            result = studentMapper.insertStudentParent(student.getId(), parent.getId(), childRelation, maxSort + 1);
        }
        return result;
    }


    /**
     * 保存业务信息
     * @patams: [request, studentId]
     * @return: com.joinus.campusbuspush.entity.BusCardBusiness
     * @Author: zxr
     * @Date: 2020/5/25 14:20
     */
    private BusCardBusiness saveBusCardBusiness(OpenBusinessParamRequest request, Long studentId) {
        BusCardBusiness busCardBusiness = this.getBusCardBusinessByStudentIdAndOpenId(studentId, request.getOpenId());
        busCardBusiness.setStudentId(studentId)
                .setPhoneNo(request.getPhoneNo())
                .setOpenId(request.getOpenId());
        if (null == busCardBusiness.getId() || busCardBusiness.getId() <= 0) {
            busCardBusiness.setProductGifted(GlobalConstants.PRODUCT_GIFTED_STATUS.NOT_SENT);
            busCardBusinessMapper.insert(busCardBusiness);
        } else {
            busCardBusinessMapper.updateById(busCardBusiness);
        }
        return busCardBusiness;
    }


    /**
     * 根据学生id查询业务表数据
     * @patams: [studentId]
     * @return: com.joinus.campusbuspush.entity.BusCardBusiness
     * @Author: zxr
     * @Date: 2020/5/25 14:17
     */
    @Override
    public BusCardBusiness getBusCardBusinessByStudentIdAndOpenId(Long studentId, String openId) {
        QueryWrapper<BusCardBusiness> businessQueryWrapper = new QueryWrapper<>();
        businessQueryWrapper.eq("student_id", studentId);
        businessQueryWrapper.eq("open_id", openId);
        BusCardBusiness busCardBusiness = busCardBusinessMapper.selectOne(businessQueryWrapper);
        if (ObjectUtil.isEmpty(busCardBusiness)) {
            busCardBusiness = new BusCardBusiness();
        }
        return busCardBusiness;
    }

}
