package com.joinus.campusbuspush.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.campusbuspush.common.GlobalConstants;
import com.joinus.campusbuspush.entity.BusCard;
import com.joinus.campusbuspush.entity.BusCardBusiness;
import com.joinus.campusbuspush.entity.BusCardWhiteList;
import com.joinus.campusbuspush.entity.pojo.request.InputParam;
import com.joinus.campusbuspush.entity.pojo.request.TMInputParamRequest;
import com.joinus.campusbuspush.entity.pojo.request.WhiteListParamRequest;
import com.joinus.campusbuspush.entity.pojo.response.TMOutputParamResponse;
import com.joinus.campusbuspush.mapper.BusCardBusinessMapper;
import com.joinus.campusbuspush.mapper.BusCardMapper;
import com.joinus.campusbuspush.mapper.BusCardWhiteListMapper;
import com.joinus.campusbuspush.service.BusCardWhiteListService;
import com.joinus.campusbuspush.util.MapperUtils;
import com.joinus.campusbuspush.util.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: campus-bus-push
 * 公交卡白名单服务层实现
 * @author: zr
 * @create: 2020-05-22 10:57
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class BusCardWhiteListServiceImpl extends BaseServiceImpl<BusCardWhiteListMapper, BusCardWhiteList> implements BusCardWhiteListService {

    @Value("${tianmai.whiteList.push.switch}")
    private Boolean pushSwitch;
    @Value("${tianmai.service.url}")
    private String tianmaiServiceUrl;
    @Value("${tianmai.service.applyType}")
    private String applyType ;
    @Value("${tianmai.service.confirmType}")
    private String confirmType;
    @Value("${tianmai.service.pushType}")
    private String pushType;
    @Value("${tianmai.service.userNo}")
    private String userNo;
    @Value("${tianmai.service.defaultOpNo}")
    private String defaultOpNo;
    @Value("${tianmai.service.terNo}")
    private String terNo;
    @Value("${tianmai.service.pushTerNo}")
    private String pushTerNo;

    private final RedisUtil redisUtil;
    private final BusCardMapper busCardMapper;
    private final BusCardWhiteListMapper busCardWhiteListMapper;
    private final BusCardBusinessMapper busCardBusinessMapper;

    @Override
    public IPage<BusCardWhiteList> selectIncrementWhitePage(WhiteListParamRequest request) {
        if (request.getSize() <= 0) {
            request.setSize(100L);
        }

        IPage<BusCardWhiteList> pages = this.lambdaQuery().select(BusCardWhiteList::getId, BusCardWhiteList::getCardNo, BusCardWhiteList::getPhoneNo, BusCardWhiteList::getUpdateTime)
                .gt(BusCardWhiteList::getId, request.getLastId()).and(i -> i.gt(BusCardWhiteList::getUpdateTime, DateUtil.parse(request.getUpdateTime()))).orderByAsc(BusCardWhiteList::getId).page(createPage(request));

        return MapperUtils.mapPage(pages, BusCardWhiteList.class);
    }

    @Override
    public IPage<BusCardWhiteList> selectAllWhitePage(WhiteListParamRequest request) {
        if (request.getSize() <= 0) {
            request.setSize(100L);
        }
        IPage<BusCardWhiteList> pages = this.lambdaQuery().select(BusCardWhiteList::getId, BusCardWhiteList::getCardNo, BusCardWhiteList::getPhoneNo, BusCardWhiteList::getUpdateTime).orderByAsc(BusCardWhiteList::getId)
                .page(createPage(request));

        return MapperUtils.mapPage(pages, BusCardWhiteList.class);
    }

    @Override
    public void updateWhiteListByBusinessId(String phoneNo, Long businessId) {
        BusCardBusiness business = busCardBusinessMapper.selectById(businessId);
        List<BusCard> busCardList = busCardMapper.getBusCardListByStudentIdAndOpenId(business.getStudentId(), business.getOpenId());
        busCardList.forEach(busCard -> updateWhiteList(phoneNo,busCard.getLogicCardNo()));
    }

    @Override
    public void updateWhiteList(String phoneNo, String logicCardNo) {
        QueryWrapper<BusCardWhiteList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("card_no", logicCardNo);
        BusCardWhiteList busCardWhiteList = busCardWhiteListMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(busCardWhiteList)) {
            busCardWhiteList = new BusCardWhiteList();
            busCardWhiteList.setCardNo(logicCardNo);
            busCardWhiteList.setPhoneNo(phoneNo);
            busCardWhiteList.insert();
        }
    }



    @Override
    public String pushWhiteList(String phoneNo, String logicCardNo){
        if (!pushSwitch){
            log.info("白名单推送功能已关闭,停止向天迈推送白名单");
            return "";
        }
        //交易号，取10位时间戳
        Long tradeNo = System.currentTimeMillis()/1000;
        if (ObjectUtil.isEmpty(redisUtil.get(GlobalConstants.SCODE_REDIS_KEY))){
            //scode过期,重新签到
            return signApply(phoneNo,logicCardNo,tradeNo);
        }else {
            String scode = redisUtil.get(GlobalConstants.SCODE_REDIS_KEY).toString();
            TMInputParamRequest request = new TMInputParamRequest();
            //推送白名单
            request.setBTYPE(pushType);
            InputParam input = new InputParam();
            input.setCARDNO(logicCardNo);
            input.setPHONE(phoneNo);
            request.setINPUT(input);
            request.setOPNO(defaultOpNo);
            request.setSCODE(scode);
            request.setTERNO(pushTerNo);
            request.setTRADENO(tradeNo);
            String result = HttpRequest.post(tianmaiServiceUrl).body(JSONUtil.toJsonStr(request), ContentType.JSON.getValue()).execute().body();
            TMOutputParamResponse response = JSONUtil.toBean(result, TMOutputParamResponse.class);
            log.info("推送白名单参数:" + JSONUtil.toJsonStr(request));
            if (GlobalConstants.TM_RESULT_SUCCESS.equals(response.getRESULT())) {
                log.info("推送白名单成功。" + result);
            }else if(GlobalConstants.TM_RESULT_NOT_SIGN.equals(response.getRESULT()) || GlobalConstants.TM_RESULT_APPLY_TIMEOUT.equals(response.getRESULT())
                    || GlobalConstants.TM_RESULT_SCODE_NOT_MATCH.equals(response.getRESULT()) || GlobalConstants.TM_RESULT_SIGN_TIMEOUT.equals(response.getRESULT())){
                //未签到或者签到过期
                log.info("需要重新签到。" + result);
                return signApply(phoneNo,logicCardNo,tradeNo);
            } else {
                log.error("推送白名单失败。" + result);
            }
            return response.getRESMSG();
        }
    }


    /**
     * 天迈签到申请
     * @patams: [phoneNo, cardNo, tradeNo]
     * @return: java.lang.String
     * @Author: zxr
     * @Date: 2020/8/19 17:47
     */
    private String signApply(String phoneNo, String cardNo, Long tradeNo){
        TMInputParamRequest request = new TMInputParamRequest();
        //签到申请
        request.setBTYPE(applyType);
        InputParam input = new InputParam();
        input.setUSERNO(userNo);
        request.setINPUT(input);
        request.setTERNO(terNo);
        request.setTRADENO(tradeNo);
        String result = HttpRequest.post(tianmaiServiceUrl).body(JSONUtil.toJsonStr(request),ContentType.JSON.getValue()).execute().body();
        TMOutputParamResponse response = JSONUtil.toBean(result,TMOutputParamResponse.class);
        if (GlobalConstants.TM_RESULT_SUCCESS.equals(response.getRESULT())){
            log.info("签到申请成功。"+result);
            return signConfirm(response.getOUTPUT().getRANDOM(),response.getOUTPUT().getOPNO(),tradeNo,phoneNo,cardNo);
        }else {
            log.info("签到申请参数:" + JSONUtil.toJsonStr(request));
            log.error("签到申请失败"+result);
            return response.getRESMSG();
        }
    }

    /**
     * 天迈推送签到确认
     * @patams: [random, opNo, tradeNo, phoneNo, cardNo]
     * @return: java.lang.String
     * @Author: zxr
     * @Date: 2020/8/19 17:47
     */
    private String signConfirm(String random, String opNo, Long tradeNo, String phoneNo, String cardNo){
        //签到确认
        TMInputParamRequest request = new TMInputParamRequest();
        request.setBTYPE(confirmType);
        InputParam input = new InputParam();
        input.setUSERNO(userNo);
        input.setPARAM(random);
        request.setINPUT(input);
        request.setOPNO(opNo);
        request.setTERNO(terNo);
        request.setTRADENO(tradeNo);
        String result = HttpRequest.post(tianmaiServiceUrl).body(JSONUtil.toJsonStr(request), ContentType.JSON.getValue()).execute().body();
        TMOutputParamResponse response = JSONUtil.toBean(result,TMOutputParamResponse.class);
        if (GlobalConstants.TM_RESULT_SUCCESS.equals(response.getRESULT())){
            log.info("签到确认成功。"+result);
            //将签到码存入redis,24h失效
            redisUtil.set(GlobalConstants.SCODE_REDIS_KEY,response.getOUTPUT().getSCODE(),24*60*60);
            return afterSignPushWhiteList(response.getOUTPUT().getSCODE(),opNo,tradeNo,phoneNo,cardNo);
        }else{
            log.info("签到确认参数:" + JSONUtil.toJsonStr(request));
            log.error("签到确认失败"+result);
            return response.getRESMSG();
        }
    }

    /**
     * 天迈签到后推送白名单
     * @patams: [scode, opNo, tradeNo, phoneNo, cardNo]
     * @return: java.lang.String
     * @Author: zxr
     * @Date: 2020/8/19 17:47
     */
    private String afterSignPushWhiteList(String scode, String opNo, Long tradeNo, String phoneNo, String cardNo){
        TMInputParamRequest request = new TMInputParamRequest();
        //推送白名单
        request.setBTYPE(pushType);
        InputParam input = new InputParam();
        input.setCARDNO(cardNo);
        input.setPHONE(phoneNo);
        request.setINPUT(input);
        request.setOPNO(opNo);
        request.setSCODE(scode);
        request.setTERNO(pushTerNo);
        request.setTRADENO(tradeNo);
        String result = HttpRequest.post(tianmaiServiceUrl).body(JSONUtil.toJsonStr(request),ContentType.JSON.getValue()).execute().body();
        TMOutputParamResponse response = JSONUtil.toBean(result,TMOutputParamResponse.class);
        log.info("推送白名单参数:"+JSONUtil.toJsonStr(request));
        if (GlobalConstants.TM_RESULT_SUCCESS.equals(response.getRESULT())){
            log.info("推送白名单成功。"+result);
        }else {
            log.error("推送白名单失败。"+result);
        }
        return response.getRESMSG();
    }
}
