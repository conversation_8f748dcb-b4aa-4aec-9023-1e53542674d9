package com.joinus.campusbuspush.service.impl;

import com.joinus.campusbuspush.entity.Student;
import com.joinus.campusbuspush.mapper.StudentMapper;
import com.joinus.campusbuspush.service.StudentService;
import org.springframework.stereotype.Service;

/**
 * @program: campus-bus-push
 * 学生相关服务实现类
 * @author: zxr
 * @create: 2020-05-15 13:51
 **/
@Service
public class StudentServiceImpl extends BaseServiceImpl<StudentMapper, Student> implements StudentService {

    @Override
    public Student checkStudentExistsByCardNo(String cardNo) {
        return baseMapper.checkStudentExistsByCardNo(cardNo);
    }
}
