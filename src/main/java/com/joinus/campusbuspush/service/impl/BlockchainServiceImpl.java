package com.joinus.campusbuspush.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Joiner;
import com.joinus.campusbuspush.common.BlockchainApiConstants;
import com.joinus.campusbuspush.common.GlobalConstants;
import com.joinus.campusbuspush.common.exception.constant.enums.CommonResponseEnum;
import com.joinus.campusbuspush.config.BusSwipeMessageConverter;
import com.joinus.campusbuspush.entity.BusSwipeMessage;
import com.joinus.campusbuspush.entity.StuFamilyAccount;
import com.joinus.campusbuspush.entity.Student;
import com.joinus.campusbuspush.entity.dto.ConCommunication;
import com.joinus.campusbuspush.mapper.StuFamilyAccountMapper;
import com.joinus.campusbuspush.service.BlockchainService;
import com.joinus.campusbuspush.util.Sm3Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 区块链相关服务实现
 * @date 2021/6/25
 */
@Slf4j
@Service
public class BlockchainServiceImpl implements BlockchainService {
    @Value("${blockchain.host}")
    private String blockchainHost;
    @Value("${blockchain.app.id}")
    private String blockchainAppId;
    @Value("${blockchain.app.secret}")
    private String blockchainAppSecret;
    @Resource
    private BusSwipeMessageConverter busSwipeMessageConverter;
    @Resource
    private StuFamilyAccountMapper stuFamilyAccountMapper;

    /**
     * createWallet
     *
     * @params: [student]
     * @return: java.lang.String
     * @Author: jxt
     * @Date: 2021/6/25 1:59 下午
     */
    @Override
    public String createWallet(Student student) {
        StuFamilyAccount account = getStuFamilyAccount(student.getId());
        if (account != null){
            return account.getPublicKey();
        }
        String familyToken = RandomUtil.randomString(6);
        Map<String, String> params = new HashMap<>();
        params.put("UserID", student.getId().toString());
        params.put("FamilyHash", familyToken);
        params.put("appId", blockchainAppId);
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        String signStr = Sm3Utils.asciiSort(params);
        String sign = Sm3Utils.sign(signStr + blockchainAppSecret).toUpperCase();
        params.put("sign", sign);
        String result = HttpRequest.post(blockchainHost + BlockchainApiConstants.createWalletApi).body(JSONUtil.toJsonStr(params), ContentType.JSON.getValue()).execute().body();
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsFalse(ObjectUtil.isEmpty(result));
        JSONObject resultJson = JSONUtil.parseObj(result);
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(GlobalConstants.BLOCKCHAIN_RESULT_SUCCESS.equals(resultJson.getStr("state")));
        String publicKey = resultJson.getStr("publicKey");
        //存储familyHash和publicKey
        //todo 调用接口增加统一标识代码
        account = new StuFamilyAccount();
        account.setStudentId(student.getId());
        account.setFamilyToken(familyToken);
        account.setPublicKey(publicKey);
        account.insert();
        return publicKey;
    }

    /**
     * getStuFamilyAccount
     * @params: [id]
     * @return: com.joinus.campusbuspush.entity.StuFamilyAccount
     * @Author: jxt
     * @Date: 2021/6/29 4:25 下午
     */
    private StuFamilyAccount getStuFamilyAccount(Long studentId) {
        QueryWrapper<StuFamilyAccount> wrapper = new QueryWrapper<>();
        wrapper.eq("student_id", studentId);
        return stuFamilyAccountMapper.selectOne(wrapper);
    }

    /**
     * queryByTxHash
     *
     * @params: [studentId, txHash]
     * @return: com.joinus.campusbuspush.entity.BusSwipeMessage
     * @Author: jxt
     * @Date: 2021/6/29 11:38 上午
     */
    @Override
    public BusSwipeMessage queryByTxHash(Long studentId, String txHash) {
        StuFamilyAccount account = getStuFamilyAccount(studentId);
        if (account == null || ObjectUtil.isEmpty(account.getPublicKey())) {
            return null;
        }
        Map<String, String> params = new HashMap<>();
        params.put("FamilyHash", account.getFamilyToken());
        params.put("txHash", txHash);
        params.put("Publickey", account.getPublicKey());
        params.put("appId", blockchainAppId);
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        String signStr = Sm3Utils.asciiSort(params);
        String sign = Sm3Utils.sign(signStr + blockchainAppSecret).toUpperCase();
        params.put("sign", sign);
        String result = HttpRequest.post(blockchainHost + BlockchainApiConstants.getTransactionApi).body(JSONUtil.toJsonStr(params), ContentType.JSON.getValue()).execute().body();
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsFalse(ObjectUtil.isEmpty(result));
        JSONObject resultJson = JSONUtil.parseObj(result);
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(GlobalConstants.BLOCKCHAIN_RESULT_SUCCESS.equals(resultJson.getStr("state")));
        CommonResponseEnum.DECRYPT_FAILED.assertIsTrue(GlobalConstants.BLOCK_DECRYPT_SUCCESS.equals(resultJson.getStr("isDecrypt")));
        String attribStr = resultJson.getStr("attribStr");
        CommonResponseEnum.DECRYPT_FAILED.assertIsFalse(ObjectUtil.isEmpty(attribStr));
        return decodeAttrStr(attribStr);
    }

    /**
     * queryByTxHashList
     *
     * @params: [studentId, txHashList]
     * @return: java.util.List<com.joinus.campusbuspush.entity.BusSwipeMessage>
     * @Author: jxt
     * @Date: 2021/6/29 11:38 上午
     */
    @Override
    public List<BusSwipeMessage> queryByTxHashList(Long studentId, List<String> txHashList) {
        List<BusSwipeMessage> list = new ArrayList<>();
        StuFamilyAccount account = getStuFamilyAccount(studentId);
        if (account == null || ObjectUtil.isEmpty(account.getPublicKey())) {
            return list;
        }
        if (ObjectUtil.isEmpty(txHashList)) {
            return list;
        }
        Map<String, String> params = new HashMap<>();
        params.put("FamilyHash", account.getFamilyToken());
        params.put("txHashs", Joiner.on(",").join(txHashList));
        params.put("Publickey", account.getPublicKey());
        params.put("appId", blockchainAppId);
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        String signStr = Sm3Utils.asciiSort(params);
        String sign = Sm3Utils.sign(signStr + blockchainAppSecret).toUpperCase();
        params.put("sign", sign);
        String result = HttpRequest.post(blockchainHost + BlockchainApiConstants.getTransactionListApi).body(JSONUtil.toJsonStr(params), ContentType.JSON.getValue()).execute().body();
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsFalse(ObjectUtil.isEmpty(result));
        JSONObject resultJson = JSONUtil.parseObj(result);
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(GlobalConstants.BLOCKCHAIN_RESULT_SUCCESS.equals(resultJson.getStr("state")));
        JSONArray blockinfolist = resultJson.getJSONArray("blockinfolist");
        blockinfolist.forEach(obj -> {
            JSONObject block = (JSONObject) obj;
            String attrStr = block.getStr("attribStr");
            if (GlobalConstants.BLOCK_DECRYPT_SUCCESS.equals(block.getStr("isDecrypt"))) {
                if (ObjectUtil.isNotEmpty(attrStr)){
                    list.add(decodeAttrStr(attrStr));
                }
            }
        });
        return list;
    }

    /**
     * buildAttrStr
     *
     * @params: [busSwipeMessage, publicKey]
     * @return: java.lang.String
     * @Author: jxt
     * @Date: 2021/6/29 11:38 上午
     */
    private String buildAttrStr(BusSwipeMessage busSwipeMessage, String publicKey) {
        StringBuilder attrSb = new StringBuilder();
        String seperator = "|";
        try {
            ConCommunication conCommunication = busSwipeMessageConverter.swipeMessageToConCommunication(busSwipeMessage);
            conCommunication.setAttrib_04(publicKey);
            conCommunication.setAttrib_15(GlobalConstants.SWIPE_TYPE_CONVERT.get(Integer.parseInt(conCommunication.getAttrib_15())));
            conCommunication.setAttrib_16(GlobalConstants.DIRECTION_CONVERT.get(Integer.parseInt(conCommunication.getAttrib_16())));
            List<Method> methodList = new ArrayList<>();

            for (int i = 1; i <= ConCommunication.class.getDeclaredFields().length; i++) {
                Method method = ConCommunication.class.getDeclaredMethod("getAttrib_" + String.format("%02d", i));
                methodList.add(method);
            }
            for (Method method : methodList) {
                attrSb.append(seperator).append(method.invoke(conCommunication));
            }
        } catch (NoSuchMethodException noSuchMethodException) {
            log.error("未找到方法:{}", noSuchMethodException.getMessage());
        } catch (IllegalAccessException illegalAccessException) {
            log.error("没有访问权限:{}", illegalAccessException.getMessage());
        } catch (InvocationTargetException invocationTargetException) {
            log.error("反射异常:{}", invocationTargetException.getMessage());
        }
        String attr = attrSb.append(seperator).toString();
        return attr;
    }

    /**
     * decodeAttrStr
     *
     * @params: [attrStr]
     * @return: com.joinus.campusbuspush.entity.BusSwipeMessage
     * @Author: jxt
     * @Date: 2021/6/29 11:38 上午
     */
    private BusSwipeMessage decodeAttrStr(String attrStr) {
        if (ObjectUtil.isEmpty(attrStr)){
            return null;
        }
        ConCommunication conCommunication = new ConCommunication();
        try {
            String[] attribArray = attrStr.split("\\|");
            List<Method> list = new ArrayList<>();
            for (int i = 1; i <= ConCommunication.class.getDeclaredFields().length; i++) {
                Method method = ConCommunication.class.getDeclaredMethod("setAttrib_" + String.format("%02d", i), String.class);
                list.add(method);
            }
            for (int i = 1; i <= list.size(); i++) {
                list.get(i - 1).invoke(conCommunication, attribArray[i]);
            }
        } catch (NoSuchMethodException noSuchMethodException) {
            log.error("未找到方法:{}", noSuchMethodException.getMessage());
        } catch (IllegalAccessException illegalAccessException) {
            log.error("没有访问权限:{}", illegalAccessException.getMessage());
        } catch (InvocationTargetException invocationTargetException) {
            log.error("反射异常:{}", invocationTargetException.getMessage());
        }
        conCommunication.setAttrib_15(String.valueOf(GlobalConstants.SWIPE_TYPE_CONVERT.indexOf(conCommunication.getAttrib_15())));
        conCommunication.setAttrib_16(String.valueOf(GlobalConstants.DIRECTION_CONVERT.indexOf(conCommunication.getAttrib_16())));
        return busSwipeMessageConverter.conCommunicationToSwipeMessage(conCommunication);
    }

}
