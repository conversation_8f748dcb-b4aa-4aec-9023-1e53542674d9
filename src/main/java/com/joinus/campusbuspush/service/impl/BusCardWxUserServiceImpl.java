package com.joinus.campusbuspush.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.joinus.campusbuspush.common.GlobalConstants;
import com.joinus.campusbuspush.common.exception.constant.enums.CommonResponseEnum;
import com.joinus.campusbuspush.entity.*;
import com.joinus.campusbuspush.mapper.*;
import com.joinus.campusbuspush.service.BusCardWxUserService;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @program: campus-bus-push
 * 微信用户相关服务实现类
 * @author: zxr
 * @create: 2020-05-15 13:51
 **/
@Service
@AllArgsConstructor
public class BusCardWxUserServiceImpl extends BaseServiceImpl<BusCardWxUserMapper, BusCardWxUser> implements BusCardWxUserService {

    private WxUserMapper wxUserMapper;
    private BusCardWxUserMapper busCardWxUserMapper;
    private BusCardBusinessMapper busCardBusinessMapper;
    private StudentMapper studentMapper;

    @Override
    public void subscribe(WxMpUser wxMpUser) {
        String openId = wxMpUser.getOpenId();
        String nickName = wxMpUser.getNickname();
        WxUser wxUser = queryWxUserByOpenId(openId);

        BusCardWxUser busCardWxUser = queryBusCardWxUserByOpenId(openId);

        if (ObjectUtil.isEmpty(wxUser)) {
            wxUser = WxUser.builder()
                    .openid(openId)
                    .nickname(nickName)
                    .sex(wxMpUser.getSex())
                    .country(wxMpUser.getCountry())
                    .province(wxMpUser.getProvince())
                    .city(wxMpUser.getCity())
                    .headimgurl(wxMpUser.getHeadImgUrl())
                    .unionId(wxMpUser.getUnionId())
                    .userType(GlobalConstants.WX_USER_TYPE)
                    .build();
            wxUserMapper.insert(wxUser);
        } else {
            wxUser.setNickname(nickName);
            wxUserMapper.updateById(wxUser);
        }

        if (ObjectUtil.isEmpty(busCardWxUser)) {
            busCardWxUser = new BusCardWxUser();
            busCardWxUser.setWxUserId(wxUser.getId());
            busCardWxUser.setOpenId(openId);
            busCardWxUser.setSubscribeStatus(GlobalConstants.WX_SUBSCRIBE_STATUS);
            busCardWxUser.setIsActive(GlobalConstants.IS_ACTIVE_EFFECT);
            busCardWxUser.setSubChannel(Integer.valueOf(wxMpUser.getQrScene()));
            busCardWxUser.insert();
        } else {
            busCardWxUser.setWxUserId(wxUser.getId());
            busCardWxUser.setSubscribeStatus(GlobalConstants.WX_SUBSCRIBE_STATUS);
            busCardWxUser.updateById();
        }
    }

    @Override
    public void unsubscribe(String openId) {
        QueryWrapper<BusCardWxUser> queryWrapper = new QueryWrapper<BusCardWxUser>();
        queryWrapper.eq("open_id", openId);
        BusCardWxUser busCardWxUser = new BusCardWxUser();
        busCardWxUser.setSubscribeStatus(GlobalConstants.WX_UN_SUBSCRIBE_STATUS);
        this.busCardWxUserMapper.update(busCardWxUser, queryWrapper);
    }


    @Override
    public boolean checkSubscribeStatus(String openId) {
        QueryWrapper<BusCardWxUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("open_id", openId);
        queryWrapper.eq("subscribe_status", GlobalConstants.WX_SUBSCRIBE_STATUS);
        BusCardWxUser busCardWxUser = busCardWxUserMapper.selectOne(queryWrapper);
        return ObjectUtil.isNotEmpty(busCardWxUser);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delBusCardBussinessAndWxUserStudent(String openId, Long studentId) {
        BusCardBusiness business = busCardBusinessMapper.selectByStudentIdAndOpenId(studentId, openId);
        CommonResponseEnum.NOT_FOUND.assertNotNull(business);
        // 删除业务关联
        this.delBusCardBusiness(business);
        // 删除亲情关联
        this.delStudentParentRelation(studentId, business.getPhoneNo());
    }

    @Override
    public BusCardWxUser queryBusCardWxUserByOpenId(String openId) {
        QueryWrapper<BusCardWxUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("open_id", openId);
        return busCardWxUserMapper.selectOne(queryWrapper);
    }

    @Override
    public WxUser queryWxUserByOpenId(String openId) {
        QueryWrapper<WxUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("openid", openId);
        queryWrapper.eq("user_type", GlobalConstants.WX_USER_TYPE);
        return wxUserMapper.selectOne(queryWrapper);
    }

    @Override
    public void updateWxUserTelNum(String openId, String phone) {
        WxUser wxUser = queryWxUserByOpenId(openId);
        if (Objects.isNull(wxUser)) {
            wxUser = WxUser.builder()
                    .openid(openId)
                    .phone(phone)
                    .userType(GlobalConstants.WX_USER_TYPE)
                    .build();
            wxUserMapper.insert(wxUser);
            BusCardWxUser busCardWxUser = queryBusCardWxUserByOpenId(openId);
            busCardWxUser.setWxUserId(wxUser.getId());
            busCardWxUser.updateById();
        } else {
            wxUser.setPhone(phone);
            wxUserMapper.updateById(wxUser);
        }
    }

    /**
     * delBusCardBusiness
     *
     * @params: [openId, studentId]
     * @return: void
     * @Author: jxt
     * @Date: 2020/11/16 4:38 下午
     **/
    @Transactional(rollbackFor = Exception.class)
    public void delBusCardBusiness(BusCardBusiness busCardBusiness) {
        QueryWrapper<Student> studentWrapper = new QueryWrapper<>();
        studentWrapper.eq("id", busCardBusiness.getStudentId());
        Student student = studentMapper.selectOne(studentWrapper);
        // 判断业务是否有效
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsFalse((busCardBusiness.getBusEndTime() != null && DateUtil.compare(busCardBusiness.getBusEndTime(), new Date()) == 1)
                || (busCardBusiness.getSubwayEndTime() != null && DateUtil.compare(busCardBusiness.getSubwayEndTime(), new Date()) == 1), "家人" + student.getStudentName() + "已开通乘车安业务，暂无法删除，有疑问请联系客服967111或4000967111");
        busCardBusiness.deleteById();
    }

    /**
     * delStudentParentRelation
     *
     * @params: [studentId]
     * @return: java.lang.String
     * @Author: jxt
     * @Date: 2020/12/8 5:35 下午
     **/
    @Transactional(rollbackFor = Exception.class)
    public void delStudentParentRelation(Long studentId, String parentPhone) {
        List<Map<String, BigDecimal>> list = studentMapper.queryStudentParentByStudentIdAndTelNum(studentId, parentPhone);
        for (Map<String, BigDecimal> map : list) {
            studentMapper.deleteStudentParentByStudentIdAndParentId(map.get("STUDENT_ID").longValue(), map.get("PARENT_ID").longValue());
        }
    }
}
