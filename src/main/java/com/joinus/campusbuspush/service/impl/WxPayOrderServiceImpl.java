package com.joinus.campusbuspush.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderResult;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.joinus.campusbuspush.common.GlobalConstants;
import com.joinus.campusbuspush.common.exception.constant.enums.CommonResponseEnum;
import com.joinus.campusbuspush.kafka.KafkaProducer;
import com.joinus.campusbuspush.entity.Class;
import com.joinus.campusbuspush.entity.*;
import com.joinus.campusbuspush.entity.pojo.response.WxPayOrderResponse;
import com.joinus.campusbuspush.mapper.*;
import com.joinus.campusbuspush.service.*;
import com.joinus.campusbuspush.util.MapperUtils;
import com.joinus.campusbuspush.util.RequestUtils;
import com.joinus.campusbuspush.util.wechat.WXPayUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: campus-bus-push
 * 微信支付订单实现类
 * @author: zxr
 * @create: 2020-05-21 10:56
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class WxPayOrderServiceImpl extends BaseServiceImpl<WxPayOrderMapper, WxPayOrder> implements WxPayOrderService {

    @Value("${wx.pay.notify.url}")
    private String notifyUrl;


    private final KafkaProducer kafkaProducer;
    private final BusCardBusinessMapper businessMapper;
    private final BusinessApplySelfMapper applySelfMapper;
    private final BusinessSelfMapper businessSelfMapper;
    private final ClassMapper classMapper;
    private final ProductSelfMapper productSelfMapper;
    private final StudentMapper studentMapper;
    private final WxPayOrderMapper wxPayOrderMapper;
    private final BusCardBusinessMapper busCardBusinessMapper;
    private final BusCarBusinessService busCarBusinessService;
    private final BusCardWhiteListService whiteListService;
    private final ProductSelfService productSelfService;
    private final WxPayService wxPayService;
    private final BusCardBusinessCouponMapper couponMapper;
    private final BusCardService busCardService;
    private final BusCardWxUserService busCardWxUserService;


    @Override
    public WxPayOrderResponse openBusiness(WxPayOrder wxPayOrder,HttpServletRequest request, boolean isGift) {
        WxPayOrderResponse orderResponse = null;
        wxPayOrder.setOrderNo(WXPayUtil.getOrderNo());
        // 计算支付金额
        ProductSelfSub productSelfSub = productSelfMapper.selectBySubId(wxPayOrder.getProductId());
        int totalFee = calcTotalFee(wxPayOrder, productSelfSub);
        wxPayOrder.setTotalFee((long)totalFee);
        //1.保存订单
        log.info("openBusiness.wxPayOrder:{}", wxPayOrder.toString());
        wxPayOrderMapper.insert(wxPayOrder);
        //2.组建微信下单参数
        String body = "业务开通-" + productSelfSub.getProductName();
        WxPayUnifiedOrderRequest orderRequest = buildWxOrderRequest(request, wxPayOrder, body);
        if (totalFee > 0 && !isGift) {
            try {
                //3.调用微信下单
                WxPayUnifiedOrderResult result = this.wxPayService.unifiedOrder(orderRequest);
                Map<String, String> payMap = new HashMap<>();
                payMap.put("appId", result.getAppid());
                payMap.put("timeStamp", WXPayUtil.getCurrentTimestamp() + "");
                payMap.put("nonceStr", WXPayUtil.generateNonceStr());
                payMap.put("signType", "MD5");
                payMap.put("package", "prepay_id=" + result.getPrepayId());
                String paySign = WXPayUtil.generateSignature(payMap, wxPayService.getConfig().getMchKey());
                orderResponse = new WxPayOrderResponse();
                orderResponse.setAppId(result.getAppid());
                orderResponse.setTimeStamp(Long.parseLong(payMap.get("timeStamp")));
                orderResponse.setNonceStr(payMap.get("nonceStr"));
                orderResponse.setPaySign(paySign);
                orderResponse.setPpackage(payMap.get("package"));
                orderResponse.setOrderNo(wxPayOrder.getOrderNo());
            } catch (WxPayException e) {
                orderResponse = null;
                log.error("业务开通Step2--->>调用微信统一下单接口失败！" + e.getMessage(), e);
            } catch (Exception e) {
                orderResponse = null;
                log.error("业务开通--->>调用业务开通接口失败！" + e.getMessage(), e);
            }
        }else {
            //支付0元，不用调微信支付，直接处理业务
            operateOrderAndBusiness(wxPayOrder.getOrderNo(), wxPayOrder.getSummary(), null);
            orderResponse = new WxPayOrderResponse();
            orderResponse.setOrderNo(wxPayOrder.getOrderNo());
            orderResponse.setPpackage("no_pay");
        }
        return orderResponse;
    }

    private int calcTotalFee(WxPayOrder wxPayOrder, ProductSelfSub productSelfSub) {
        Long couponId = wxPayOrder.getCouponId();
        Coupon coupon = this.validCoupon(wxPayOrder.getOpenId(), couponId);
        int couponFee = 0;
        if (coupon != null){
            couponFee = coupon.getCouponAmount() * 100;
        }
        return Math.max(productSelfSub.getCurrentFee() - couponFee, 0);
    }

    @Override
    public boolean openBusinessNotify(String xmlData) {
        boolean result = false;
        try {
            final WxPayOrderNotifyResult notifyResult = this.wxPayService.parseOrderNotifyResult(xmlData);
            log.info("微信支付异步响应数据为：{}", notifyResult.toString());
            // 如果付款成功，校验返回的订单金额是否与商户侧的订单金额一致，防止数据泄漏导致出现“假通知”，造成资金损失
            checkTotalFee(notifyResult);
            // 处理业务
            operateOrderAndBusiness(notifyResult.getOutTradeNo(), notifyResult.getAttach(), notifyResult.getTransactionId());
            result = true;
        } catch (WxPayException e) {
            log.error("微信支付响应处理失败 WxPayException={},{}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("微信支付响应处理异常 Exception={},{}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    public boolean getOrderResult(String orderNo){
        boolean result = false;
        try {
            // 判断通知是否已经处理过
            if (!checkHaveOperated(orderNo)) {
                //根据订单号去调用微信接口查询订单支付状态
                WxPayOrderQueryResult queryResult = wxPayService.queryOrder(null, orderNo);
                if ("SUCCESS".equals(queryResult.getReturnCode()) && "SUCCESS".equals(queryResult.getResultCode())
                        && "SUCCESS".equals(queryResult.getTradeState())) {
                    operateOrderAndBusiness(orderNo, queryResult.getAttach(), queryResult.getTransactionId());
                    result = true;
                }
            } else {
                result = true;
            }
        } catch (WxPayException e){
            log.error("微信支付查询处理失败 WxPayException={},{}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("微信支付查询处理异常 Exception={},{}", e.getMessage(), e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WxPayOrderResponse giftOrder(WxPayOrder wxPayOrder, HttpServletRequest request) {
        BusCardBusiness business = busCarBusinessService.selectByStudentIdAndOpenId(wxPayOrder.getStudentId(), wxPayOrder.getOpenId());
        CommonResponseEnum.VALID_ERROR.assertIsFalse(GlobalConstants.PRODUCT_GIFTED_STATUS.SEND.equals(business.getProductGifted()), "此用户已赠送过套餐");
        WxPayOrderResponse response = openBusiness(wxPayOrder, request, true);
        busCarBusinessService.lambdaUpdate()
                .set(BusCardBusiness::getProductGifted, GlobalConstants.PRODUCT_GIFTED_STATUS.SEND)
                .eq(BusCardBusiness::getId, wxPayOrder.getBusinessId())
                .update();
        return response;
    }

    /**
     * 处理订单，开通业务
     * @params: [orderNo,cardNo]
     * @return: void
     * @Author: jxt
     * @Date: 2020/9/5 5:26 下午
     **/
    @Transactional(rollbackFor = Exception.class)
    public void operateOrderAndBusiness(String orderNo, String logicCardNo, String transactionId) {
        //对订单处理进行同步，防止重复操作
        synchronized (orderNo.intern()) {
            // 判断通知是否已经处理过
            if (!checkHaveOperated(orderNo)) {
                QueryWrapper<WxPayOrder> wxPayOrderQueryWrapper = new QueryWrapper<>();
                wxPayOrderQueryWrapper.eq("order_no", orderNo);
                WxPayOrder wxPayOrder = wxPayOrderMapper.selectOne(wxPayOrderQueryWrapper);
                wxPayOrder.setTransactionId(transactionId);
                //处理订单状态
                operateBusiness(wxPayOrder);
                //更新业务数据状态
                updateBusiness(wxPayOrder);
                //更新优惠券状态
                updateCouponStatus(wxPayOrder);
                //推送白名单到天迈
                pushWhiteListToTianMai(logicCardNo, orderNo);
            }
        }
    }

    /**
     * 更新优惠券状态
     * @params: [wxPayOrder] 
     * @return: void
     * @Author: jxt
     * @Date: 2020/11/17 3:58 下午
     **/
    @Transactional(rollbackFor = Exception.class)
    public void updateCouponStatus(WxPayOrder wxPayOrder) {
        if (wxPayOrder.getCouponId() != null){
            Coupon coupon = couponMapper.selectById(wxPayOrder.getCouponId());
            coupon.setCouponStatus(GlobalConstants.COUPON_STATUS.USED);
            coupon.setOrderId(wxPayOrder.getId());
            couponMapper.updateById(coupon);
        }
    }

    /**
     * 处理订单表置为已付款
     * @patams: [notifyResult]
     * @return: void
     * @Author: zxr
     * @Date: 2020/5/21 20:28
     */
    @Transactional(rollbackFor = Exception.class)
    public void operateBusiness(WxPayOrder wxPayOrder) {
        // 同步微信支付订单号，该订单号也用于判断是否处理过通知
        wxPayOrder.setPayCompleteTime(DateUtil.date());
        wxPayOrder.setStatus(3);
        wxPayOrderMapper.updateById(wxPayOrder);
        if (log.isInfoEnabled()) {
            log.info("订单状态已更新，更新后的订单信息为：{}", wxPayOrder);
        }

    }

    /**
     * 更新业务
     * @patams: [wxPayOrder]
     * @return: void
     * @Author: zxr
     * @Date: 2020/5/21 20:28
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBusiness(WxPayOrder wxPayOrder) {
        BusCardBusiness busCardBusiness = businessMapper.selectById(wxPayOrder.getBusinessId());
        ProductSelfSub productSelfSub = productSelfMapper.selectBySubId(wxPayOrder.getProductId());
        this.saveBusinessOpened(wxPayOrder, productSelfSub);

        //更新公交卡白名单
        whiteListService.updateWhiteListByBusinessId(busCardBusiness.getPhoneNo(),busCardBusiness.getId());

        //微信发送开通信息
        busCardBusiness.setProductId(wxPayOrder.getProductId());
        kafkaProducer.sendOpenProduct(JSONUtil.toJsonStr(busCardBusiness));
    }

    /**
     * 保存业务已开通记录,如果是混合套餐,则进行拆分
     * @patams: [wxPayOrder, productSelf]
     * @return: void
     * @Author: zxr
     * @Date: 2020/8/24 16:03
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBusinessOpened(WxPayOrder wxPayOrder, ProductSelfSub productSelfSub) {
        Long productSelfSubId = productSelfSub.getId();
        BusCardBusiness busCardBusiness = busCardBusinessMapper.selectById(wxPayOrder.getBusinessId());
        // 检查是否为混合套餐
        List<ProductSelfRatio> productSelfRatios = productSelfService.selectRatioProductList(productSelfSubId);
        if (productSelfRatios.isEmpty()) {
            insertOrUpdateBusinessOpened(productSelfSub, wxPayOrder, productSelfSub.getCurrentFee(), busCardBusiness);
        }else {
            //若是混合套餐，最后一项费用=总费用-前n项费用累加之和
            ProductSelfSub productSelfSubReal;
            int sumFee = 0;
            for (int i = 0; i < productSelfRatios.size() - 1; i++) {
                int actualAmount = (int) (productSelfSub.getCurrentFee() * productSelfRatios.get(i).getRatio());
                productSelfSubReal = productSelfMapper.selectBySubId(productSelfRatios.get(i).getProductId());
                insertOrUpdateBusinessOpened(productSelfSubReal, wxPayOrder, actualAmount, busCardBusiness);
                sumFee += actualAmount;
            }
            int restFee = productSelfSub.getCurrentFee() - sumFee;
            productSelfSubReal = productSelfMapper.selectBySubId(productSelfRatios.get(productSelfRatios.size() - 1).getProductId());
            insertOrUpdateBusinessOpened(productSelfSubReal, wxPayOrder, restFee, busCardBusiness);
        }
        //更新业务结束时间
        busCardBusinessMapper.updateById(busCardBusiness);
    }

    /**
     * 保存t_bus_card_business_opened数据
     * @patams: [productSelf, wxPayOrder, ratio]
     * @return: void
     * @Author: zxr
     * @Date: 2020/8/24 16:01
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertOrUpdateBusinessOpened(ProductSelfSub productSelfSub, WxPayOrder wxPayOrder, int actualAmount, BusCardBusiness busCardBusiness) {
        String type = JSONArray.parseArray(productSelfSub.getProductDesc()).getJSONObject(1).getString("description");
        Date startTime = getOpenStartTime(type,busCardBusiness);
        Date endTime = DateUtil.offsetDay(startTime, productSelfSub.getPeriod());
        if (GlobalConstants.PRODECT_DESC_BUS.equals(type)){
            busCardBusiness.setBusEndTime(endTime);
        }else {
            busCardBusiness.setSubwayEndTime(endTime);
        }

        BusCardBusinessOpened busCardBusinessOpened = new BusCardBusinessOpened();
        busCardBusinessOpened.setActualAmount(actualAmount)
                .setBusinessId(wxPayOrder.getBusinessId())
                .setStudentId(wxPayOrder.getStudentId())
                // 这里混合套餐实际保存的是对应的公交/地铁套餐
                .setProductId(productSelfSub.getProductId())
                .setSubProductId(productSelfSub.getId())
                .setOrderNo(wxPayOrder.getOrderNo())
                .setStartTime(startTime)
                .setEndTime(endTime);
        busCardBusinessOpened.insert();

        this.saveBusinessSelf(productSelfSub,busCardBusinessOpened);
    }

    /**
     * 获取已开通业务最后结束时间,如果如果没有数据就返回当前时间
     * @params: [productDesc, busCardBusiness]
     * @return: java.util.Date
     * @Author: jxt
     * @Date: 2020/9/1 5:16 下午
     **/
    private Date getOpenStartTime(String type, BusCardBusiness busCardBusiness) {
        Date lastEndTime = new Date();
        if (GlobalConstants.PRODECT_DESC_BUS.equals(type)){
            Date endTime = busCardBusiness.getBusEndTime();
            if (endTime != null && lastEndTime.compareTo(endTime) < 0){
                lastEndTime = endTime;
            }
        }else {
            Date endTime = busCardBusiness.getSubwayEndTime();
            if (endTime != null && lastEndTime.compareTo(endTime) < 0){
                lastEndTime = endTime;
            }
        }
        return DateUtil.beginOfDay(lastEndTime);
    }


    /**
     * 保存到自推广业务开通表
     * @patams: [productSelf, busCardBusiness]
     * @return: int
     * @Author: zxr
     * @Date: 2020/6/11 10:57
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBusinessSelf(ProductSelfSub productSelfSub, BusCardBusinessOpened busCardBusinessOpened) {
        BusCardBusiness busCardBusiness =  businessMapper.selectById(busCardBusinessOpened.getBusinessId());
        QueryWrapper<BusinessSelf> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("BUSINESS_TYPE", GlobalConstants.BUSINESS_TYPE)
                .eq("BUSINESS_ID", busCardBusiness.getId());
        BusinessSelf businessSelf = businessSelfMapper.selectOne(queryWrapper);
        Student student = studentMapper.selectById(busCardBusiness.getStudentId());
        Class aClass = classMapper.selectById(student.getClassId());
        int operType = GlobalConstants.APPLY_OPER_TYPE_RENEW;
        if (ObjectUtil.isEmpty(businessSelf)) {
            businessSelf = new BusinessSelf();
            operType = GlobalConstants.APPLY_OPER_TYPE_OPEN;
        }
        if (ObjectUtil.isNotEmpty(aClass)){
            businessSelf.setGradeId(String.valueOf(aClass.getGradeId()))
                    .setClassId(String.valueOf(aClass.getId()))
                    .setSchoolId(String.valueOf(aClass.getSchoolId()));
        }
        businessSelf.setBusinessType(GlobalConstants.BUSINESS_TYPE)
                .setBusinessId(String.valueOf(busCardBusiness.getId()))
                .setProductId(String.valueOf(productSelfSub.getProductId()))
                .setStudentId(String.valueOf(busCardBusiness.getStudentId()))
                .setSubProductId(String.valueOf(productSelfSub.getId()))
                .setPhoneNum(busCardBusiness.getPhoneNo())
                .setStartTime(busCardBusinessOpened.getStartTime())
                .setEndTime(busCardBusinessOpened.getEndTime());
        businessSelf.insertOrUpdate();
        BusinessApplySelf businessApplySelf = MapperUtils.map(businessSelf,BusinessApplySelf.class);
        businessApplySelf.setOperType(operType);
        businessApplySelf.setSubProductFee(productSelfSub.getCurrentFee()).setRes(1).setOperTime(new Date());
        applySelfMapper.insert(businessApplySelf);
    }

    /**
     * 检查是否已经处理过了
     * @patams: [orderNo]
     * @return: boolean
     * @Author: zxr
     * @Date: 2020/5/21 20:08
     */
    private boolean checkHaveOperated(String orderNo) {
        QueryWrapper<WxPayOrder> wxPayOrderQueryWrapper = new QueryWrapper<>();
        wxPayOrderQueryWrapper.eq("order_no", orderNo);
        WxPayOrder wxPayOrder = wxPayOrderMapper.selectOne(wxPayOrderQueryWrapper);
        if (StrUtil.isNotEmpty(wxPayOrder.getTransactionId())) {
            return wxPayOrder.getStatus() == 3;
        }
        return false;
    }

    /**
     * 校验金额是否与微信支付返回的一致
     * @patams: [notifyResult]
     * @return: void
     * @Author: zxr
     * @Date: 2020/5/21 20:08
     */
    private void checkTotalFee(WxPayOrderNotifyResult notifyResult) throws Exception {

        if (WxPayConstants.ResultCode.SUCCESS.equals(notifyResult.getReturnCode())) {
            if (WxPayConstants.WxpayTradeStatus.SUCCESS.equals(notifyResult.getResultCode())) {
                String outTradeNo = notifyResult.getOutTradeNo();
                QueryWrapper<WxPayOrder> wxPayOrderQueryWrapper = new QueryWrapper<>();
                wxPayOrderQueryWrapper.eq("order_no", outTradeNo);
                WxPayOrder wxPayOrder = wxPayOrderMapper.selectOne(wxPayOrderQueryWrapper);
                if (wxPayOrder != null) {
                    int localTotalFee = Math.toIntExact(wxPayOrder.getTotalFee());
                    if (localTotalFee != notifyResult.getTotalFee()) {
                        throw new Exception("微信通知订单金额与商户侧订单金额不一致，假通知！");
                    }
                } else {
                    throw new Exception("订单不存在，假通知！");
                }
            } else {
                throw new Exception("业务处理失败！");
            }
        } else {
            throw new Exception("微信支付失败！");
        }
    }



    /**
     * 构建微信订单信息
     * @patams: [request, wxPayOrder]
     * @return: com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest
     * @Author: zxr
     * @Date: 2020/5/25 15:45
     */
    public WxPayUnifiedOrderRequest buildWxOrderRequest(HttpServletRequest request, WxPayOrder wxPayOrder, String body) {
        WxPayUnifiedOrderRequest orderRequest = new WxPayUnifiedOrderRequest();
        // 商品描述
        orderRequest.setBody(body);
        // 商户订单号
        orderRequest.setOutTradeNo(wxPayOrder.getOrderNo());
        // 总金额
        orderRequest.setTotalFee(wxPayOrder.getTotalFee().intValue());
        // 通知地址
        orderRequest.setNotifyUrl(notifyUrl);
        // 交易类型
        String trade_type = "JSAPI";
        orderRequest.setTradeType(trade_type);
        // 终端IP
        String spbill_create_ip = RequestUtils.getIpAddress(request);
        orderRequest.setSpbillCreateIp(spbill_create_ip);
        // openid
        String openId = wxPayOrder.getOpenId();
        orderRequest.setOpenid(openId);
        // cardNo
        if (StringUtils.isNotEmpty(wxPayOrder.getSummary()) && !"undefined".equals(wxPayOrder.getSummary())){
            orderRequest.setAttach(wxPayOrder.getSummary());
        }
        return orderRequest;
    }

    /**
     * 验证优惠券
     * @params: [openId, couponCode] 
     * @return: com.joinus.campusbuspush.entity.Coupon
     * @Author: jxt
     * @Date: 2020/11/17 3:17 下午
     **/
    private Coupon validCoupon(String openId, Long couponId) {
        if (couponId != null){
            QueryWrapper<Coupon> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("ID", couponId);
            queryWrapper.eq("OPEN_ID", openId);
            Coupon coupon = couponMapper.selectOne(queryWrapper);
            // 判断优惠码是否存在，兑换过或者已过兑换期
            CommonResponseEnum.NOT_FOUND.assertIsFalse(coupon == null, "优惠券不存在");
            CommonResponseEnum.VALID_ERROR.assertIsFalse(coupon.getCouponStatus() != GlobalConstants.COUPON_STATUS.EXCHANGED, "优惠券已使用");
            Date currentTime = new Date();
            CommonResponseEnum.VALID_ERROR.assertIsFalse(DateUtil.compare(currentTime, coupon.getUseStartTime()) < 0 , "未到使用有效期");
            CommonResponseEnum.VALID_ERROR.assertIsFalse(DateUtil.compare(currentTime, coupon.getUseEndTime()) > 0, "优惠券已过期");
            return coupon;
        }
        return null;
    }

    /**
     * 向天迈推送白名单
     * @patams: [logicCardNo, tradeNo]
     * @return: void
     * @Author: zxr
     * @Date: 2020/8/24 10:39
     */
    private void pushWhiteListToTianMai(String logicCardNo, String tradeNo){
        //cardNo不为空，说明有卡号，不是续费，需要推送
        if (StringUtils.isNotEmpty(logicCardNo)) {
            //根据外部订单号查询手机号和卡号
            QueryWrapper<WxPayOrder> wxPayOrderQueryWrapper = new QueryWrapper<>();
            wxPayOrderQueryWrapper.eq("order_no", tradeNo);
            WxPayOrder wxPayOrder = wxPayOrderMapper.selectOne(wxPayOrderQueryWrapper);
            BusCardBusiness busCardBusiness = businessMapper.selectById(wxPayOrder.getBusinessId());
            String phoneNo = busCardBusiness.getPhoneNo();
            busCardService.pushWhiteList(logicCardNo, phoneNo);
        }
    }
}
