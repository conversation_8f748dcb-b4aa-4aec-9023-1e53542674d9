package com.joinus.campusbuspush.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.joinus.campusbuspush.common.exception.constant.enums.CommonResponseEnum;
import com.joinus.campusbuspush.entity.ProductSelfRatio;
import com.joinus.campusbuspush.entity.ProductSelfSub;
import com.joinus.campusbuspush.mapper.ProductSelfMapper;
import com.joinus.campusbuspush.mapper.ProductSelfRatioMapper;
import com.joinus.campusbuspush.service.ProductSelfService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @program: campus-bus-push
 * 业务套餐相关服务实现类
 * @author: zxr
 * @create: 2020-05-19 17:51
 **/
@Service
@AllArgsConstructor
public class ProductSelfServiceImpl extends BaseServiceImpl<ProductSelfMapper, ProductSelfSub> implements ProductSelfService {

    private ProductSelfMapper productSelfMapper;
    private ProductSelfRatioMapper productSelfRatioMapper;

    @Override
    public List<Map<String,Object>> selectProductList() {
        List<ProductSelfSub> productList = productSelfMapper.selectProductList();
        List<ProductSelfSub> productSubList = productSelfMapper.selectProductSubList();
        List<Map<String,Object>> resultList = new ArrayList<>();
        for (ProductSelfSub product:productList) {
            List<ProductSelfSub> subProducts = new ArrayList();
            for (ProductSelfSub productSelfSub:productSubList) {
                if (product.getProductId().equals(productSelfSub.getProductId())){
                    subProducts.add(productSelfSub);
                }
            }
            Map<String,Object> productMap = new HashMap<>();
            productMap.put("name",product.getProductName());
            JSONArray productInfo = JSONArray.parseArray(product.getProductDesc());
            CommonResponseEnum.SYSTEM_EXCEPTION.assertIsTrue(productInfo.size() >= 4,product.getProductName()+"缺少设置项");
            productMap.put("title",productInfo.getJSONObject(0).getString("description"));
            productMap.put("order",productInfo.getJSONObject(2).getIntValue("description"));
            productMap.put("description",productInfo.getJSONObject(3).getString("description"));
            productMap.put("productList",subProducts);
            resultList.add(productMap);
        }
        Collections.sort(resultList, (o1, o2) -> {
            if(MapUtil.getInt(o1,"order") > MapUtil.getInt(o2,"order")){
                return 1;
            } else if (MapUtil.getInt(o1,"order") < MapUtil.getInt(o2,"order")) {
                return -1;
            }
            return 0;
        });
        return resultList;
    }

    @Override
    public List<ProductSelfRatio> selectRatioProductList(Long parentProductId){
        QueryWrapper<ProductSelfRatio> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("PARENT_PRODUCT_ID",parentProductId);
        return productSelfRatioMapper.selectList(queryWrapper);
    }

    @Override
    public Long selectGiftProductId() {
        return productSelfMapper.selectGiftProductId();
    }
}
