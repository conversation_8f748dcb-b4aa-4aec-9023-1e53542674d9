package com.joinus.campusbuspush.service;

import com.joinus.campusbuspush.entity.BusCardBusiness;
import com.joinus.campusbuspush.entity.pojo.request.OpenBusinessParamRequest;
import com.joinus.campusbuspush.entity.pojo.response.BusCardBusinessResponse;
import com.joinus.campusbuspush.entity.pojo.response.OpenBusinessResponse;

import java.util.Date;
import java.util.List;

/**
 * 公交卡业务服务层
 * @Author: zxr
 * @Date: 2020/5/15 11:20
 */
public interface BusCarBusinessService extends BaseService<BusCardBusiness> {

    /**
     * 查询是否开通业务且在有效期
     * @patams: [cardNo]
     * @return: com.joinus.campusbuspush.entity.BusCardProduct
     * @Author: zxr
     * @Date: 2020/5/16 21:34
     */
    List<BusCardBusiness> selectBusCardBusiness(String cardNo,int swipeType);

    /**
     * 根据用户openId 查询已开通业务的学生列表
     * @patams: [openId]
     * @return: java.util.List<com.joinus.campusbuspush.controller.response.StudentBusinessResponse>
     * @Author: zxr
     * @Date: 2020/5/18 16:54
     */
    List<BusCardBusinessResponse> selectStudentList(String openId);

    /**
     * 检查公交卡是否可用
     * @patams: [request]
     * @return: java.lang.String
     * @Author: zxr
     * @Date: 2020/5/19 17:57
     */
    String checkBusCard(OpenBusinessParamRequest request);
    
    /**
     * 开通业务
     * @patams: [request]
     * @return: boolean
     * @Author: zxr
     * @Date: 2020/5/18 21:06
     */
    OpenBusinessResponse saveBusinessInfo(OpenBusinessParamRequest request);

    /**
     * 查询业务将到期的数据和过期数据
     * @patams: [day]
     * @return: java.util.List<com.joinus.campusbuspush.entity.BusCardBusiness>
     * @Author: zxr
     * @Date: 2020/5/22 11:35
     */
    List<BusCardBusiness> selectExpiringBusiness();

    /**
     * 获取已开通业务最后结束时间,如果如果没有数据就返回当前时间
     * @patams: [studentId, productId]
     * @return: java.util.Date
     * @Author: zxr
     * @Date: 2020/8/24 18:00
     */
    Date getOpenedLastEndTime(Long studentId, Long productId);

    /**
     * 根据studentId查询业务
     * @params: [studentId, openId]
     * @return: com.joinus.campusbuspush.entity.BusCardBusiness
     * @Author: jxt
     * @Date: 2020/9/2 10:18 上午
     **/
    BusCardBusiness selectByStudentIdAndOpenId(Long studentId, String openId);

    /**
     * getBusCardBusinessByStudentIdAndOpenId
     * @params: [studentId, openId]
     * @return: com.joinus.campusbuspush.entity.BusCardBusiness
     * @Author: jxt
     * @Date: 2020/11/5 4:42 下午
     **/
    BusCardBusiness getBusCardBusinessByStudentIdAndOpenId(Long studentId, String openId);
}
