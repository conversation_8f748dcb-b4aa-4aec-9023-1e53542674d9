package com.joinus.campusbuspush.service;

import com.joinus.campusbuspush.entity.BusCard;
import com.joinus.campusbuspush.entity.BusCardBusiness;
import com.joinus.campusbuspush.entity.pojo.request.OpenBusinessParamRequest;

import java.util.List;

/**
 * 公交卡信息服务层
 * @Author: zxr
 * @Date: 2020/5/15 11:20
 */
public interface BusCardService extends BaseService<BusCard> {
    /**
     * 查询业务下绑定的所有公交卡id
     * @patams: [businessId]
     * @return: java.util.List<java.lang.Long>
     * @Author: zxr
     * @Date: 2020/8/19 15:06
     */
    List<Long> getCardIdListByBusinessId(Long businessId);

    /**
     * 查询学生绑定的所有卡信息
     * @patams: [studentId, openId]
     * @return: java.util.List<com.joinus.campusbuspush.entity.BusCard>
     * @Author: zxr
     * @Date: 2020/8/19 15:06
     */
    List<BusCard> getCardListByStudentIdAndOpenId(Long studentId, String openId);

    /**
     * 公交卡添加
     * @patams: [studentId, busCardNo]
     * @return: int
     * @Author: zxr
     * @Date: 2020/5/20 09:58
     */
    int addBusCardBinding(Long studentId, String busCardNo, String openId);

    /**
     * 保存业务和公交卡绑定关系
     * @patams: [busCardBusiness, busCardNo]
     * @return: int
     * @Author: zxr
     * @Date: 2020/8/20 17:04
     */
    int saveBusCardBinding(BusCardBusiness busCardBusiness, String busCardNo);

    /**
     * 公交卡启用
     * @patams: [studentId, busCardNo]
     * @return: int
     * @Author: zxr
     * @Date: 2020/5/20 09:58
     */
    int enableBusCard(Long studentId, String busCardNo, String openId);

    /**
     * 公交卡停用
     * @patams: [request]
     * @return: boolean
     * @Author: zxr
     * @Date: 2020/5/20 10:13
     */
    int disableBusCard(OpenBusinessParamRequest request);

    /**
     * 公交卡注销
     * @patams: [request]
     * @return: int
     * @Author: zxr
     * @Date: 2020/5/20 09:58
     */
    int cancelBasCard(OpenBusinessParamRequest request);

    /**
     * queryByLogicCardCode
     * @params: [logicCardCode]
     * @return: com.joinus.campusbuspush.entity.BusCard
     * @Author: jxt
     * @Date: 2020/11/19 1:08 下午
     **/
    BusCard queryByLogicCardCode(String logicCardCode);

    /**
     * pushWhiteList
     * @params: [logicCardNo, phoneNo]
     * @return: void
     * @Author: jxt
     * @Date: 2020/11/19 4:50 下午
     **/
    void pushWhiteList(String logicCardNo, String phoneNo);
}
