package com.joinus.campusbuspush.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.campusbuspush.entity.Coupon;
import com.joinus.campusbuspush.entity.pojo.request.CouponParamRequest;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-17
 */
public interface CouponService extends BaseService<Coupon> {
    /**
     * 兑换优惠券
     * @params: [openId, couponCode] 
     * @return: void
     * @Author: jxt
     * @Date: 2020/11/17 1:40 下午
     **/
    void exchangeCoupon(String openId, String couponCode);

    /**
     * getCouponByOpenId
     * @params: [openId]
     * @return: com.joinus.campusbuspush.entity.Coupon
     * @Author: jxt
     * @Date: 2020/11/17 1:46 下午
     **/
    Coupon getCouponByOpenId(String openId);

    /**
     * 优惠券分页列表
     * @params: [couponParamRequest]
     * @return: com.baomidou.mybatisplus.core.metadata.IPage
     * @Author: jxt
     * @Date: 2020/11/18 10:24 上午
     **/
    IPage listCoupon(CouponParamRequest couponParamRequest);

    /**
     * 批量生成优惠券
     * @params: [couponParamRequest] 
     * @return: void
     * @Author: jxt
     * @Date: 2020/11/18 10:33 上午
     **/
    void genrateCoupons(CouponParamRequest couponParamRequest);
}
