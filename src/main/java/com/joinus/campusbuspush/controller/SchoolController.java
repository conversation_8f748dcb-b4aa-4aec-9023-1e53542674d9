package com.joinus.campusbuspush.controller;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.campusbuspush.entity.pojo.request.SchoolParamRequest;
import com.joinus.campusbuspush.entity.School;
import com.joinus.campusbuspush.service.SchoolService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: campus-bus-push
 * 学校相关控制器
 * @author: zxr
 * @create: 2020-05-15 13:50
 **/
@AllArgsConstructor
@RestController
@RequestMapping("/school")
public class SchoolController extends BaseController{

    private SchoolService schoolService;

    /**
     * 查询学校
     * @patams: [request]
     * @return: cn.hutool.json.JSONObject
     * @Author: zxr
     * @Date: 2020/5/18 09:03
     */
    @PostMapping("/selectSchoolList")
    public Object selectSchool(@RequestBody SchoolParamRequest request){
        IPage<School> page = schoolService.selectSchoolPage(request);
        return success(page);
    }


}
