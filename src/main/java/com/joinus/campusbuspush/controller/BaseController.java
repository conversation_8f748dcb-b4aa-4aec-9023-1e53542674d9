package com.joinus.campusbuspush.controller;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONObject;
import com.joinus.campusbuspush.common.GlobalConstants;
import com.joinus.campusbuspush.common.exception.constant.enums.CommonResponseEnum;
import com.joinus.campusbuspush.common.exception.exception.BaseException;
import com.joinus.campusbuspush.entity.OutputObject;
import com.joinus.campusbuspush.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Map;

/**
 * @program: campus-bus-push
 * 定义基础控制器
 * @author: zxr
 * @create: 2020-05-14 14:36
 **/
@Slf4j
public class BaseController {

    @Resource
    private RedisUtil redisUtil;

    /**
     * 请求时使用url地址后缀传参的方式,校验token是否有效
     */
    protected boolean checkToken() {
        Map<String, String[]> map = getServletRequest().getParameterMap();
        String token = map.get(GlobalConstants.ACCESS_TOKEN)[0];
        return redisUtil.hasKey(SecureUtil.md5(token));
    }

    /**
     * 获取request里参数
     *
     * @param name 参数名
     * @return 参数值
     */
    protected String getPara(String name) {
        return getServletRequest().getParameter(name);
    }

    /**
     * 获取当前客户端session对象
     *
     * @return
     */
    protected HttpSession getSession() {
        return getServletRequest().getSession();
    }

    /**
     * 从thread local获取网络上下文
     */
    protected HttpServletRequest getServletRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes servletRequestAttributes;
        if (requestAttributes instanceof ServletRequestAttributes) {
            servletRequestAttributes = (ServletRequestAttributes) requestAttributes;
            return servletRequestAttributes.getRequest();
        }
        return null;
    }

    /**
     * 请求错误返回, 输入错误内容
     * @patams: [err]
     * @return: cn.hutool.json.JSONObject
     * @Author: zxr
     * @Date: 2020/5/16 19:09
     */
    protected JSONObject fail(String err) {
        throw new BaseException(CommonResponseEnum.REQUEST_UNSUCCESSFUL.getCode(), err);
    }

    /**
     * 请求错误返回
     * @patams: [code, err]
     * @return: cn.hutool.json.JSONObject
     * @Author: zxr
     * @Date: 2020/5/16 19:08
     */
/*    protected JSONObject fail(Integer code, String err) {
        JSONObject object = new JSONObject();
        object.set("status", "fail");
        object.set("msg", err);
        object.set("code", code);
        return object;
    }

    *//**
     * 请求错误返回,有data数据
     * @patams: [code, err]
     * @return: cn.hutool.json.JSONObject
     * @Author: zxr
     * @Date: 2020/5/16 19:08
     *//*
    protected JSONObject fail(Integer code, String err,Object data) {
        JSONObject object = new JSONObject();
        object.set("status", "fail");
        object.set("msg", err);
        object.set("data", data);
        object.set("code", code);
        return object;
    }

    *//**
     * 请求成功
     *
     * @return
     *//*
    protected JSONObject success() {
        JSONObject object = new JSONObject();
        object.set("status", CommonResponseEnum.SUCCESS.getMessage());
        object.set("msg", "ok");
        object.set("code", CommonResponseEnum.SUCCESS.getCode());
        return object;
    }

    *//**
     * 请求成功
     *
     * @param msg 返回的内容
     * @return
     *//*
    protected JSONObject success(String msg) {
        JSONObject object = new JSONObject();
        object.set("status", CommonResponseEnum.SUCCESS.getMessage());
        object.set("msg", msg);
        object.set("code", CommonResponseEnum.SUCCESS.getCode());
        return object;
    }


    *//**
     * 请求成功, 返回请求数据
     * @param: data 数据
     * @return: object
     * @Author: zxr
     * @Date: 2020/5/14 14:51
     *//*
    protected JSONObject success(Object data) {
        JSONObject object = new JSONObject(true);
        object.set("status", CommonResponseEnum.SUCCESS.getMessage());
        object.set("data", data);
        object.set("code", CommonResponseEnum.SUCCESS.getCode());
        return object;
    }

    *//**
     * 请求成功, 返回请求数据
     * @param: data 数据
     * @param: msg 标识
     * @return: object
     * @Author: zxr
     * @Date: 2020/5/14 14:51
     *//*
    protected JSONObject success(Object data, String msg) {
        JSONObject object = new JSONObject();
        object.set("status", CommonResponseEnum.SUCCESS.getMessage());
        object.set("data", data);
        object.set("msg", msg);
        object.set("code", CommonResponseEnum.SUCCESS.getCode());
        return object;
    }*/


    /**
     * 请求错误返回
     * @patams: [code, err]
     * @return: cn.hutool.json.JSONObject
     * @Author: zxr
     * @Date: 2020/5/16 19:08
     */
    protected OutputObject fail(Integer code, String err) {
        OutputObject object = OutputObject.builder()
                .status("fail")
                .msg(err)
                .code(code)
                .build();
        return object;
    }

    /**
     * 请求错误返回,有data数据
     * @patams: [code, err]
     * @return: cn.hutool.json.JSONObject
     * @Author: zxr
     * @Date: 2020/5/16 19:08
     */
    protected OutputObject fail(Integer code, String err,Object data) {
        OutputObject object = OutputObject.builder()
                .status("fail")
                .msg(err)
                .data(data)
                .code(code)
                .build();
        return object;
    }

    /**
     * 请求成功
     *
     * @return
     */
    protected OutputObject success() {
        OutputObject object = OutputObject.builder()
                .status(CommonResponseEnum.SUCCESS.getMessage())
                .msg("ok")
                .code(CommonResponseEnum.SUCCESS.getCode())
                .build();
        return object;
    }

    /**
     * 请求成功
     *
     * @param msg 返回的内容
     * @return
     */
    protected OutputObject success(String msg) {
        OutputObject object = OutputObject.builder()
                .status(CommonResponseEnum.SUCCESS.getMessage())
                .msg(msg)
                .code(CommonResponseEnum.SUCCESS.getCode())
                .build();
        return object;
    }


    /**
     * 请求成功, 返回请求数据
     * @param: data 数据
     * @return: object
     * @Author: zxr
     * @Date: 2020/5/14 14:51
     */
    protected OutputObject success(Object data) {
        OutputObject object = OutputObject.builder()
                .status(CommonResponseEnum.SUCCESS.getMessage())
                .data(data)
                .code(CommonResponseEnum.SUCCESS.getCode())
                .build();
        return object;
    }

    /**
     * 请求成功, 返回请求数据
     * @param: data 数据
     * @param: msg 标识
     * @return: object
     * @Author: zxr
     * @Date: 2020/5/14 14:51
     */
    protected OutputObject success(Object data, String msg) {
        OutputObject object = OutputObject.builder()
                .status(CommonResponseEnum.SUCCESS.getMessage())
                .data(data)
                .msg(msg)
                .code(CommonResponseEnum.SUCCESS.getCode())
                .build();
        return object;
    }


}
