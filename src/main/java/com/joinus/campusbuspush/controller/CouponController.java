package com.joinus.campusbuspush.controller;


import com.joinus.campusbuspush.service.CouponService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  优惠券前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-17
 */
@Slf4j
@Api(tags = "优惠券")
@AllArgsConstructor
@RestController
@RequestMapping("/coupon")
public class CouponController extends BaseController{
    private CouponService couponService;

    @ApiOperation(value = "优惠券兑换")
    @PostMapping("/exchange")
    @ApiImplicitParams({
            @ApiImplicitParam(name="couponCode",value="兑换码",dataType="string", paramType = "query"),
            @ApiImplicitParam(name="openId",value="微信用户openId",dataType="string", paramType = "query")
    })
    public Object exchangeCoupon(String openId, String couponCode){
        couponService.exchangeCoupon(openId, couponCode);
        return success();
    }

    @ApiOperation(value = "优惠券查询")
    @PostMapping("/get")
    @ApiImplicitParam(name="openId",value="微信用户openId",dataType="string", paramType = "query")
    public Object getCoupon(String openId){
        return success(couponService.getCouponByOpenId(openId));
    }
}
