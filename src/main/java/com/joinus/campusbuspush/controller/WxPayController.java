package com.joinus.campusbuspush.controller;

import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.joinus.campusbuspush.entity.pojo.response.WxPayOrderResponse;
import com.joinus.campusbuspush.entity.WxPayOrder;
import com.joinus.campusbuspush.service.WxPayOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.service.ResponseMessage;

import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR> <PERSON>
 */
@Slf4j
@Api(tags = "微信支付")
@RestController
@AllArgsConstructor
@RequestMapping("/wx/pay")
public class WxPayController extends BaseController {

    private WxMpService wxMpService;
    private WxPayService wxService;
    private WxPayOrderService wxPayOrderService;

    /**
     * 创建订单接口
     *
     * @patams: [request]
     * @return: cn.hutool.json.JSONObject
     * @Author: zxr
     * @Date: 2020/5/21 10:54
     */
    @ApiOperation(value = "创建订单接口")
    @PostMapping("/createOrder")
    public Object createOrder(HttpServletRequest request, @RequestBody WxPayOrder wxPayOrder) throws WxPayException {
        WxPayOrderResponse response = wxPayOrderService.openBusiness(wxPayOrder, request, false);
        return success(response);
    }


    @ApiOperation(value = "支付回调通知处理")
    @PostMapping("/notify/order")
    public String parseOrderNotifyResult(@RequestBody String xmlData) throws WxPayException {
        wxPayOrderService.openBusinessNotify(xmlData);
        return WxPayNotifyResponse.success("成功");
    }

    @ApiOperation(value = "获取调用jsapi时所需要的签名", notes = "获取调用jsapi时所需要的签名，返回jsapi的config信息", response = ResponseMessage.class)
    @GetMapping("/jsapi/signature")
    public Object getIdentityInfos(@RequestParam String url) {
        try {
            WxJsapiSignature signature = wxMpService.createJsapiSignature(url);
            log.debug(signature.toString());
            return success(signature);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return fail("获取调用jsapi时所需要的签名失败！");
        }

    }

    @ApiOperation(value = "查询订单状态")
    @PostMapping("/order/result")
    @ApiImplicitParam(name = "orderNo", value = "订单号", dataType = "string", paramType = "query")
    public Object getOrderResult(@RequestParam String orderNo) throws WxPayException {
        return success(wxPayOrderService.getOrderResult(orderNo));
    }

}

