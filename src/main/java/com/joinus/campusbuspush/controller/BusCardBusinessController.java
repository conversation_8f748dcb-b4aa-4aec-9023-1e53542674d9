package com.joinus.campusbuspush.controller;

import com.joinus.campusbuspush.common.GlobalConstants;
import com.joinus.campusbuspush.common.exception.constant.enums.CommonResponseEnum;
import com.joinus.campusbuspush.entity.BusCardBusiness;
import com.joinus.campusbuspush.entity.Student;
import com.joinus.campusbuspush.entity.WxPayOrder;
import com.joinus.campusbuspush.entity.pojo.request.OpenBusinessParamRequest;
import com.joinus.campusbuspush.entity.pojo.response.OpenBusinessResponse;
import com.joinus.campusbuspush.entity.pojo.response.WxPayOrderResponse;
import com.joinus.campusbuspush.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpOAuth2AccessToken;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * @program: campus-bus-push
 * 公交卡业务相关控制器
 * @author: zxr
 * @create: 2020-05-18 11:39
 **/
@RequiredArgsConstructor
@RestController
@RequestMapping("busCardBusiness")
@Slf4j
@Api(tags = "公交卡业务相关接口")
public class BusCardBusinessController extends BaseController {


    private final BusCardService busCardService;
    private final BusCarBusinessService busCarBusinessService;
    private final BusCardWxUserService busCardWxUserService;
    private final WxMpService wxMpService;
    private final ProductSelfService productSelfService;
    private final BusSwipeMessageService busSwipeMessageService;
    private final StudentService studentService;
    private final WxPayOrderService wxPayOrderService;
    @Value("${bus.school.id}")
    private Long busSchoolId;
    @Value("${bus.class.id}")
    private Long busClassId;
    @Value("${check.open}")
    private boolean checkOpen;
    @Value("${check.open.student.and.old:true}")
    private boolean checkOpenStudentAndOld;

    @ApiOperation(value = "根据code获取用户的openId")
    @GetMapping("/getOpenId/{code}")
    public Object getOpenId(@PathVariable("code") String code) throws WxErrorException {
        WxMpOAuth2AccessToken auth2AccessToken = wxMpService.oauth2getAccessToken(code);
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertNotNull(auth2AccessToken, "openId获取失败");
        String openId = auth2AccessToken.getOpenId();
        return success(openId);

    }

    @ApiOperation(value = "查询套餐列表")
    @GetMapping("/getMeal")
    public Object getMeal() {
        return success(productSelfService.selectProductList());
    }

    @ApiOperation(value = "保存业务信息(预开通)")
    @PostMapping("/saveBusinessInfo")
    public Object saveBusinessInfo(@RequestBody OpenBusinessParamRequest request) {
        this.validOpenBusinessParamRequest(request);
        request.setSchoolId(busSchoolId);
        request.setClassId(busClassId);
        //调用一卡通接口进行一致性验证
        if (request.getCardType() == GlobalConstants.CARD_TYPE_COMMON) {
            if (checkOpen) {
                CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsFalse(request.getCardType() == GlobalConstants.CARD_TYPE_COMMON, "乘车卡号错误，非老年卡或学生卡");
            }
        } else {
            if (checkOpenStudentAndOld) {
                busCarBusinessService.checkBusCard(request);
            }
        }
        OpenBusinessResponse response = busCarBusinessService.saveBusinessInfo(request);
        return success(response);
    }

    @ApiOperation(value = "公交卡启用")
    @PostMapping("/enableBusCard")
    public Object enableBusCard(@RequestBody OpenBusinessParamRequest request) {
        int result = busCardService.enableBusCard(request.getStudentId(), request.getCardNo(), request.getOpenId());
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(result > 0, "公交卡启用操作失败");
        return success();

    }


    @ApiOperation(value = "公交卡停用")
    @PostMapping("/disableBusCard")
    public Object disableBusCard(@RequestBody OpenBusinessParamRequest request) {
        int result = busCardService.disableBusCard(request);
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(result > 0, "公交卡停用操作失败");
        return success();

    }

    @ApiOperation(value = "公交卡注销")
    @PostMapping("/cancelBasCard")
    public Object cancelBasCard(@RequestBody OpenBusinessParamRequest request) {
        int result = busCardService.cancelBasCard(request);
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(result > 0, "公交卡注销操作失败");
        return success();
    }

    @ApiOperation(value = "增加新公交卡号")
    @PostMapping("/addBasCard")
    public Object addBusCard(@RequestBody OpenBusinessParamRequest request) {
        this.validOpenBusinessParamRequest(request);
        // 传的只有卡号，学生id，需要获取姓名，身份证号，手机号
        Long studendId = request.getStudentId();
        Student student = studentService.getById(studendId);
        request.setStudentName(student.getStudentName());
        BusCardBusiness busCardBusiness = busCarBusinessService.getBusCardBusinessByStudentIdAndOpenId(studendId, request.getOpenId());
        request.setPhoneNo(busCardBusiness.getPhoneNo());
        // 调用一卡通验证
        if (request.getCardType() == GlobalConstants.CARD_TYPE_COMMON) {
            if (checkOpen) {
                CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsFalse(request.getCardType() == GlobalConstants.CARD_TYPE_COMMON, "乘车卡号错误，非老年卡或学生卡");
            }
        } else {
            busCarBusinessService.checkBusCard(request);
        }
        //验证卡号是否存在且是否是同一个人
        Student cardStudent = studentService.checkStudentExistsByCardNo(request.getCardNo());
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(cardStudent == null || student.getId().equals(cardStudent.getId()),"此卡号归属于其他用户，无法添加");
        int result = busCardService.addBusCardBinding(studendId, request.getCardNo(), request.getOpenId());
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsFalse(result == 0, "添加新卡号操作失败");
        return success();
    }

    @ApiOperation(value = "查询最近六个月消费信息")
    @GetMapping("/getExpenseListSixMouth/{studentId}/{openId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "studentId", value = "学生id", dataType = "long", paramType = "path", example = "3805027"),
            @ApiImplicitParam(name = "openId", value = "openId", dataType = "string", paramType = "path")
    })
    public Object getExpenseListSixMouth(@PathVariable("studentId") Long studentId, @PathVariable("openId") String openId) {
        return success(busSwipeMessageService.getExpenseListSixMouth(studentId, openId));
    }


    @ApiOperation(value = "查询消费信息详情")
    @GetMapping("/getExpenseListDetail/{month}/{studentId}/{swipeType}/{openId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "studentId", value = "学生id", dataType = "long", paramType = "path", example = "3805027"),
            @ApiImplicitParam(name = "month", value = "月份", dataType = "string", paramType = "path"),
            @ApiImplicitParam(name = "swipeType", value = "刷卡类型，0.公交 1.地铁 99.公交+地铁", dataType = "int", paramType = "path", example = "99"),
            @ApiImplicitParam(name = "openId", value = "openId", dataType = "string", paramType = "path")
    })
    public Object getExpenseListDetail(@PathVariable("studentId") Long studentId, @PathVariable("month") String month, @PathVariable("swipeType") int swipeType, @PathVariable("openId") String openId) {
        return success(busSwipeMessageService.getExpenseListDetail(studentId, month, swipeType, openId));
    }

    @ApiOperation(value = "解绑某人")
    @PostMapping("/unbind")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "studentId", value = "学生id", dataType = "long", paramType = "query", example = "3805027"),
            @ApiImplicitParam(name = "openId", value = "微信用户openId", dataType = "string", paramType = "query")
    })
    public Object unbind(Long studentId, String openId) {
        busCardWxUserService.delBusCardBussinessAndWxUserStudent(openId, studentId);
        return success();
    }

    /**
     * 校验参数乘车卡号
     *
     * @params: [request]
     * @return: void
     * @Author: jxt
     * @Date: 2020/11/16 4:15 下午
     **/
    private void validOpenBusinessParamRequest(OpenBusinessParamRequest request) {
        if (request.getCardNo().startsWith("66604")) {
            request.setCardType(GlobalConstants.CARD_TYPE_STUDENT);
        } else if (request.getCardNo().startsWith("66605")) {
            request.setCardType(GlobalConstants.CARD_TYPE_OLD);
        } else if (request.getCardNo().startsWith("666")) {
            request.setCardType(GlobalConstants.CARD_TYPE_COMMON);
        }
        CommonResponseEnum.VALID_ERROR.assertIsFalse(request.getCardNo().trim().length() != 14 || request.getCardType() == null, "乘车卡号错误");
    }


    @ApiOperation(value = "注册成功赠送三个月套餐")
    @PostMapping("/giftOrder")
    public Object giftOrder(@RequestBody OpenBusinessParamRequest paramRequest, HttpServletRequest request) {
        //赠送的套餐id
        WxPayOrder wxPayOrder = new WxPayOrder();
        wxPayOrder.setPayTypeId(3);
        wxPayOrder.setStudentId(paramRequest.getStudentId());
        wxPayOrder.setBusinessId(busCarBusinessService.getBusCardBusinessByStudentIdAndOpenId(paramRequest.getStudentId(), paramRequest.getOpenId()).getId());
        wxPayOrder.setOpenId(paramRequest.getOpenId());
        wxPayOrder.setProductId(productSelfService.selectGiftProductId());
        WxPayOrderResponse response = wxPayOrderService.giftOrder(wxPayOrder, request);
        return success(response);
    }
}
