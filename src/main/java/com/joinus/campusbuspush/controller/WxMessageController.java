package com.joinus.campusbuspush.controller;

import com.joinus.campusbuspush.entity.dto.OpenedMessageParam;
import com.joinus.campusbuspush.service.BusSwipeMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Api(tags = "微信推送")
@RestController
@AllArgsConstructor
@RequestMapping("/wx/message")
public class WxMessageController extends BaseController {

    BusSwipeMessageService busSwipeMessageService;

    @ApiOperation(value = "测试发送消息")
    @PostMapping("/send")
    public Object send(@NonNull @RequestBody OpenedMessageParam request) {
        log.info("sendMessage,request:{}", request.toString());
        busSwipeMessageService.sendMessage(request);
        return success();
    }
}
