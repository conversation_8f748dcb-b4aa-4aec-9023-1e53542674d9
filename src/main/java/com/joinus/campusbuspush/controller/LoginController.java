package com.joinus.campusbuspush.controller;

import com.joinus.campusbuspush.common.annotations.PhoneValidated;
import com.joinus.campusbuspush.entity.pojo.request.SmsRequest;
import com.joinus.campusbuspush.service.JoinusInnerApiService;
import com.joinus.campusbuspush.service.SchoolService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@AllArgsConstructor
@RestController
@RequestMapping("/sms")
@Slf4j
public class LoginController extends BaseController {

    @Autowired
    private JoinusInnerApiService joinusInnerApiService;

    @ApiOperation(value = "获取登录验证码")
    @PostMapping    public Object getLoginSms(HttpServletRequest request, @RequestBody SmsRequest smsRequest) throws Exception {
        log.info("LoginController.getLoginSms request:{}", smsRequest);
//        joinusInnerApiService.sendLoginSms(phoneNum, ticket);
        return success();
    }

    @ApiOperation(value = "通过手机号+验证码登录")
    @PostMapping("/validation")
    public Object loginByMobileAndCode(HttpServletRequest request,
                                       @RequestBody SmsRequest smsRequest) throws Exception {
        log.info("LoginController.loginByMobileAndCode request:{}", smsRequest);
        return success();
//        boolean smsCodePass = joinusInnerApiService.verifyLoginSms(mobile, smsCode);
//        if (!smsCodePass) {
//            return fail("验证码错误");
//        }
//        return success();
    }
}
