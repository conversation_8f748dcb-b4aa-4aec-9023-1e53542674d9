package com.joinus.campusbuspush.controller;


import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.campusbuspush.common.GlobalConstants;
import com.joinus.campusbuspush.common.exception.constant.enums.CommonResponseEnum;
import com.joinus.campusbuspush.entity.pojo.request.CouponParamRequest;
import com.joinus.campusbuspush.entity.pojo.response.CouponResponse;
import com.joinus.campusbuspush.service.CouponService;
import com.joinus.campusbuspush.util.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  优惠券管理控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-17
 */
@Slf4j
@Api(tags = "优惠券管理")
@AllArgsConstructor
@RestController
@RequestMapping("/coupon/manage")
public class CouponManageController extends BaseController{
    private CouponService couponService;

    @ApiOperation(value = "优惠券列表")
    @PostMapping("/list")
    public Object listCoupon(CouponParamRequest couponParamRequest){
        return success(couponService.listCoupon(couponParamRequest));
    }

    @ApiOperation(value = "优惠券列表导出")
    @PostMapping("/export")
    public void exportCoupon(CouponParamRequest couponParamRequest, HttpServletRequest request, HttpServletResponse response){
        couponParamRequest.setCurrent(1L);
        couponParamRequest.setSize(500L);
        IPage<CouponResponse> page = couponService.listCoupon(couponParamRequest);
        if (null != page) {
            OutputStream os = null;
            String fileName = URLEncoder.encode("乘车安代金券.xlsx");
            try {
                response.setContentType("application/vnd.ms-excel");
                final String userAgent = request.getHeader("USER-AGENT");
                try {
                    String finalFileName = null;
                    if(org.apache.commons.lang3.StringUtils.contains(userAgent, "MSIE")){//IE浏览器
                        finalFileName = URLEncoder.encode(fileName,"UTF-8");
                    }else if(org.apache.commons.lang3.StringUtils.contains(userAgent, "Mozilla")){//google,火狐浏览器
                        finalFileName = new String(fileName.getBytes(), "ISO8859-1");
                    }else{
                        finalFileName = URLEncoder.encode(fileName,"UTF-8");//其他浏览器
                    }
                    response.setHeader("Content-Disposition", "attachment; filename=\"" + finalFileName + "\"");//这里设置一下让浏览器弹出下载提示框，而不是直接在浏览器中打开
                } catch (UnsupportedEncodingException e) {
                    response.setHeader("Content-Disposition", "attachment;filename=coupon.xlsx");
                }
                os = response.getOutputStream();
                List<CouponResponse> result = page.getRecords();
                long totalPage = page.getPages();
                if (totalPage > 1) {
                    // 从第二页开始查
                    for (int i = 2; i <= totalPage; i++) {
                        couponParamRequest.setCurrent((long)i);
                        IPage rdo = couponService.listCoupon(couponParamRequest);
                        result.addAll(rdo.getRecords());
                    }
                }
                List<Map<String,Object>> list = new ArrayList<>();
                for (CouponResponse coupon : result) {
                    Map map = BeanUtils.describe(coupon);
                    map.put("couponStatus", GlobalConstants.COUPON_STATUS_MAP.get(coupon.getCouponStatus()));
                    list.add(map);
                }
                String[] excelHeader = {"兑换码","金额","生成时间","兑换有效期","使用有效期","兑换状态","兑换人姓名","兑换人手机号","使用套餐","付款金额"};
                String[] key = {"couponCode","couponAmount","createTime","exchangeTimePeriod","useTimePeriod","couponStatus","studentName","phoneNo","productName","totalFee"};
                XSSFWorkbook wb = ExcelUtil.exportExcel(list,"代金券",excelHeader,key);
                ExcelUtil.writeWB(os, wb);
                os.close();
            } catch (Exception e) {
                e.printStackTrace();
                try {
                    os.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    @ApiOperation(value = "优惠券批量生成")
    @PostMapping("/generate")
    public Object genrateCoupons(CouponParamRequest couponParamRequest){
        CommonResponseEnum.VALID_ERROR.assertIsFalse(StringUtils.isBlank(couponParamRequest.getExchangeDateBegin()), "兑换开始时间不能为空");
        CommonResponseEnum.VALID_ERROR.assertIsFalse(StringUtils.isBlank(couponParamRequest.getExchangeDateEnd()), "兑换截止时间不能为空");
        CommonResponseEnum.VALID_ERROR.assertIsFalse(StringUtils.isBlank(couponParamRequest.getUseDateBegin()), "使用开始时间不能为空");
        CommonResponseEnum.VALID_ERROR.assertIsFalse(StringUtils.isBlank(couponParamRequest.getUseDateEnd()), "使用截止时间不能为空");
        CommonResponseEnum.VALID_ERROR.assertIsFalse(couponParamRequest.getSize() <= 0, "生成数量必须大于0");
        CommonResponseEnum.VALID_ERROR.assertIsFalse(couponParamRequest.getCouponAmount() <= 0, "优惠券金额必须大于0");
        CommonResponseEnum.VALID_ERROR.assertIsFalse(DateUtil.parse(couponParamRequest.getUseDateEnd()).compareTo(DateUtil.parse(couponParamRequest.getExchangeDateEnd())) < 0, "使用截止日期必须大于兑换截止日期");
        couponService.genrateCoupons(couponParamRequest);
        return success();
    }
}
