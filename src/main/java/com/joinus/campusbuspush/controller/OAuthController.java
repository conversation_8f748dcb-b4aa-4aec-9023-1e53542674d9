package com.joinus.campusbuspush.controller;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.campusbuspush.common.GlobalConstants;
import com.joinus.campusbuspush.common.exception.constant.enums.CommonResponseEnum;
import com.joinus.campusbuspush.entity.pojo.request.OauthParamRequest;
import com.joinus.campusbuspush.entity.pojo.response.OauthParamResponse;
import com.joinus.campusbuspush.util.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: campus-bus-push
 * 认证授权控制器
 * @author: zxr
 * @create: 2020-05-15 16:42
 **/
@RequiredArgsConstructor
@RestController
@RequestMapping("/oauth")
@Slf4j
public class OAuthController extends BaseController {

    /**
     * 当前类存在@Value注解的情况下,会与@AllArgsConstructor冲突,需要使用@RequiredArgsConstructor,并将注入的bean置为final
     */
    private final RedisUtil redisUtil;

    @Value("${oauth.url}")
    private String oauthUrl;

    /**
     * 获取认证token
     */
    @PostMapping("/token/getToken")
    public Object getToken(@RequestBody OauthParamRequest request) {
        log.info("OAuthController.getToken request:{}", request);
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(request.getGrant_type().equals(GlobalConstants.GRANT_TYPE_CLIENT_CREDENTIAL), "不合法的认证类型");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("grant_type", request.getGrant_type());
        paramMap.put("scope", GlobalConstants.OAUTH_SCOPE_READ);
        String cipher = Base64Encoder.encode(request.getClient_id() + ":" + request.getClient_secret());
        String headerMsg = StrUtil.builder("Basic ").append(cipher).toString();
        String result = HttpRequest.post(oauthUrl)
                .contentType(ContentType.FORM_URLENCODED.getValue())
                .header(Header.AUTHORIZATION, headerMsg)
                .form(paramMap).execute().body();
        JSONObject resultJson = JSONUtil.parseObj(result);
        if (StrUtil.isNotEmpty(resultJson.getStr("message"))){
            log.error("调用oauth认证服务器错误:{}",result);
            return fail("认证失败,请与管理员核实");
        }
        OauthParamResponse response = JSONUtil.toBean(resultJson, OauthParamResponse.class);
        boolean saveFlag = redisUtil.set(SecureUtil.md5(response.getAccess_token()), response.getAccess_token(), response.getExpires_in());
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(saveFlag, "认证失败,请重新认证");
        return success(response);


    }

}
