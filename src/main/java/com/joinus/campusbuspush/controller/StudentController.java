package com.joinus.campusbuspush.controller;

import com.joinus.campusbuspush.entity.pojo.response.BusCardBusinessResponse;
import com.joinus.campusbuspush.service.BusCarBusinessService;
import com.joinus.campusbuspush.service.BusCardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @program: campus-bus-push
 * 学生相关控制器
 * @author: zxr
 * @create: 2020-05-18 11:46
 **/
@AllArgsConstructor
@RestController
@RequestMapping("/student")
@Api(tags = "学生相关接口")
@Slf4j
public class StudentController extends BaseController{

    private BusCarBusinessService busCarBusinessService;
    private BusCardService busCardService;

    @ApiOperation(value = "查询用户下的学生列表")
    @GetMapping("/getStudentList/{openId}")
    @ApiImplicitParam(name="openId",value="微信用户openId",dataType="string", paramType = "query")
    public Object getStudentList(@PathVariable("openId") String openId) {
        List<BusCardBusinessResponse> result = busCarBusinessService.selectStudentList(openId);
        return success(result);
    }

    @ApiOperation(value = "查询学生已绑定的卡列表")
    @GetMapping("/getCardList/{studentId}/{openId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name="studentId",value="学生id",dataType="long", paramType = "path", example = "3805027"),
            @ApiImplicitParam(name="openId",value="openId",dataType="String", paramType = "path")
    })
    public Object getCardList(@PathVariable("studentId") Long studentId, @PathVariable("openId") String openId){
        return success(busCardService.getCardListByStudentIdAndOpenId(studentId, openId));
    }

    @ApiOperation(value = "查询学生业务信息")
    @GetMapping("/getBizEndTime/{studentId}/{openId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name="studentId",value="学生id",dataType="long", paramType = "path", example = "3805027"),
            @ApiImplicitParam(name="openId",value="openId",dataType="String", paramType = "path")
    })
    public Object getBizEndTime(@PathVariable("studentId") Long studentId, @PathVariable("openId") String openId){
        return success(busCarBusinessService.selectByStudentIdAndOpenId(studentId, openId));
    }
}
