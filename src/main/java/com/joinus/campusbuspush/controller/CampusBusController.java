package com.joinus.campusbuspush.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.campusbuspush.common.GlobalConstants;
import com.joinus.campusbuspush.common.exception.constant.enums.CommonResponseEnum;
import com.joinus.campusbuspush.entity.*;
import com.joinus.campusbuspush.entity.pojo.request.TransactionCallbackRequest;
import com.joinus.campusbuspush.entity.pojo.request.WhiteListParamRequest;
import com.joinus.campusbuspush.service.BusCarBusinessService;
import com.joinus.campusbuspush.service.BusCardService;
import com.joinus.campusbuspush.service.BusCardWhiteListService;
import com.joinus.campusbuspush.service.BusSwipeMessageService;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: campus-bus-push
 * 校园公交相关控制器
 * @author: zxr
 * @create: 2020-05-16 16:40
 **/
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/ijx")
public class CampusBusController extends BaseController {

    private BusCarBusinessService busCarBusinessService;
    private BusSwipeMessageService busSwipeMessageService;
    private BusCardWhiteListService busCardWhiteListService;
    private BusCardService busCardService;

    /**
     * 刷卡信息推送
     * @param: swipeParamRequest
     * @return: 推送结果
     * @Author: zxr
     * @Date: 2020/5/16 16:44
     */
    @PostMapping("/sendSwipeInfo")
    public Object sendSwipeInfo(@NonNull @Valid @RequestBody BusSwipeMessage request) {
        log.info("sendSwipeInfo,request:{}", request.toString());
        CommonResponseEnum.PERMISSIONS_DENIED.assertIsTrue(checkToken());
        return saveAndSendSwipeMessage(request);

    }
    /**
     * 保存并推送信息
     * @params: [request] 
     * @return: void
     * @Author: jxt
     * @Date: 2020/9/2 11:39 上午
     **/
    private OutputObject saveAndSendSwipeMessage(@RequestBody @NonNull @Valid BusSwipeMessage request) {
        //逻辑卡号转换印刻卡号
        String logicCardNo = request.getCardNo();
        BusCard busCard = busCardService.queryByLogicCardCode(logicCardNo);
        if (busCard == null){
            return fail(CommonResponseEnum.REQUEST_UNSUCCESSFUL.getCode(), StrUtil.format("未找到对应的交通卡号:{}", logicCardNo));
        }
        List<BusCardBusiness> busCardBusinessList = busCarBusinessService.selectBusCardBusiness(busCard.getCardNo(),request.getSwipeType());
        if (CollectionUtil.isEmpty(busCardBusinessList)) {
            return fail(CommonResponseEnum.BUSINESS_EXPIRED.getCode(), CommonResponseEnum.BUSINESS_EXPIRED.getMessage());
        }
        request.setStudentId(busCardBusinessList.get(0).getStudentId());
        request.setCardNo(busCard.getCardNo());
        if (request.getAmountType() == null) {
            request.setAmountType(GlobalConstants.AMOUNT_TYPE_MONTHLY_TICKET);
        }
        boolean saveFlag = busSwipeMessageService.saveSwipeMessage(request);
        if (!saveFlag) {
            log.error("消费信息保存失败:{}", request);
        }
        return success("保存成功");
    }

    /**
     * 刷卡信息批量推送
     * @params: [msgList] 
     * @return: cn.hutool.json.JSONObject
     * @Author: jxt
     * @Date: 2020/9/2 11:34 上午
     **/
    @PostMapping("/sendSwipeInfoBatch")
    public Object sendSwipeInfoList(@NonNull @Valid @RequestBody List<BusSwipeMessage> msgList) {
        log.info("sendSwipeInfo,request:{}", msgList.toString());
        List<OutputObject> resultList = new ArrayList<>();
        for (BusSwipeMessage request: msgList) {
            OutputObject result = saveAndSendSwipeMessage(request);
            resultList.add(result);
        }
        return success(resultList);
    }

    /**
     * 增量获取已开通业务的公交卡号白名单
     * @patams: [id]
     * @return: cn.hutool.json.JSONObject
     * @Author: zxr
     * @Date: 2020/5/17 20:38
     */
    @PostMapping("/getIncrementWhiteList")
    public Object getIncrementWhiteList(@RequestBody WhiteListParamRequest request) {
        log.info("getIncrementWhiteList,request:{}", request.toString());
        CommonResponseEnum.PERMISSIONS_DENIED.assertIsTrue(checkToken());
        IPage<BusCardWhiteList> page = busCardWhiteListService.selectIncrementWhitePage(request);
        List<BusCardWhiteList> list = page.getRecords();
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(list.size() > 0, "未查询到相关数据");
        return success(list);

    }

    /**
     * 全量获取已开通业务的公交卡号白名单
     * @patams: [id]
     * @return: cn.hutool.json.JSONObject
     * @Author: zxr
     * @Date: 2020/5/17 20:42
     */
    @PostMapping("/getWhiteListAll")
    public Object getIncrementAll(@RequestBody WhiteListParamRequest request) {
        log.info("getWhiteListAll,request:{}", request.toString());
        CommonResponseEnum.PERMISSIONS_DENIED.assertIsTrue(checkToken());
        IPage<BusCardWhiteList> page = busCardWhiteListService.selectAllWhitePage(request);
        List<BusCardWhiteList> list = page.getRecords();
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(list.size() > 0, "未查询到相关数据");
        return success(list);

    }

    @RequestMapping("/transaction/callback")
    public Object transactionCallback(@RequestBody TransactionCallbackRequest request){
        log.info("transactionCallback,request:{}", request);
        //根据cid更新对应的txHash txHash不为空不更新，考虑微信推送是否也要挪到这里
        try {
            boolean isDeal = false;
            if (GlobalConstants.BLOCKCHAIN_RESULT_SUCCESS.equals(request.getState())) {
                UpdateWrapper<BusSwipeMessage> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("tx_hash", request.getTxHash());
                updateWrapper.eq("tx_id", request.getCid());
                updateWrapper.isNull("tx_hash");
                busSwipeMessageService.update(updateWrapper);
                isDeal = true;
                this.sendMessage(request);
            }else {
                log.error("上链失败：{}", request);
            }
            if (isDeal) {
                return new JSONObject().set("state", GlobalConstants.BLOCKCHAIN_RESULT_SUCCESS);
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("上链回调异常：{},{}", request, e.getMessage());
            return new JSONObject().set("state", GlobalConstants.BLOCKCHAIN_RESULT_FAIL);
        }
        return new JSONObject().set("state", GlobalConstants.BLOCKCHAIN_RESULT_FAIL);
    }
    @Async
    public void sendMessage(TransactionCallbackRequest request) {
        BusSwipeMessage busSwipeMessage = busSwipeMessageService.lambdaQuery()
                .eq(BusSwipeMessage::getTxId, request.getCid())
                .one();
        List<BusCardBusiness> busCardBusinessList = busCarBusinessService.selectBusCardBusiness(busSwipeMessage.getCardNo(),busSwipeMessage.getSwipeType());
        for (BusCardBusiness busCardBusiness : busCardBusinessList) {
            busSwipeMessage.setOpenId(busCardBusiness.getOpenId());
            busSwipeMessage.setStudentName(busCardBusiness.getStudentName());
            busSwipeMessageService.sendNewSwipeMessage(busSwipeMessage);
        }
    }
}
