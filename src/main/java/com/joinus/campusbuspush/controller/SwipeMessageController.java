package com.joinus.campusbuspush.controller;

import cn.hutool.core.util.ObjectUtil;
import com.joinus.campusbuspush.common.exception.constant.enums.CommonResponseEnum;
import com.joinus.campusbuspush.entity.BusSwipeMessage;
import com.joinus.campusbuspush.entity.Student;
import com.joinus.campusbuspush.service.BlockchainService;
import com.joinus.campusbuspush.service.SchoolService;
import com.joinus.campusbuspush.service.StudentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(tags = "刷卡信息")
@AllArgsConstructor
@RestController
@RequestMapping("/swipeInfo")
public class SwipeMessageController extends BaseController {

    private BlockchainService blockchainService;
    private StudentService studentService;
    private SchoolService schoolService;

    @GetMapping("/get")
    @ApiOperation(value = "查询刷卡信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "studentId", value = "学生id", dataType = "long", paramType = "path", example = "3805027"),
            @ApiImplicitParam(name = "txHash", value = "块哈希", dataType = "string", paramType = "path")
    })
    @CrossOrigin
    public Object getSwipeInfo(@RequestParam Long studentId, @RequestParam String txHash) {
        BusSwipeMessage swipeMessage = blockchainService.queryByTxHash(studentId, txHash);
        CommonResponseEnum.REQUEST_UNSUCCESSFUL.assertIsTrue(ObjectUtil.isNotNull(swipeMessage), "未查询到相关数据");
        Student student = studentService.getById(studentId);
        swipeMessage.setStudentName(student.getStudentName());
        String region = schoolService.getSchoolRegionByStudentId(studentId);
        swipeMessage.setRegion(region);
        return success(swipeMessage);
    }
}
