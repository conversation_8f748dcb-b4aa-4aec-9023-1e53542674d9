package com.joinus.campusbuspush.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.campusbuspush.entity.BusSwipeMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公交卡刷卡信息相关mapper
 * @Author: zxr
 * @Date: 2020/5/14 13:53
*/
@Mapper
public interface BusSwipeMessageMapper extends BaseMapper<BusSwipeMessage> {

    /**
     * 按月合计查询进6个月刷卡信息合计（月票）
     * @patams: [studentId]
     * @return: java.util.List<com.joinus.campusbuspush.entity.BusSwipeMessage>
     * @Author: zxr
     * @Date: 2020/5/20 14:35
     */
    List<BusSwipeMessage> getExpenseListSixMouth(Long studentId);

    /**
     * 根据卡号查询消费记录详情
     * @patams: [cardNo, month]
     * @return: java.util.List<com.joinus.campusbuspush.entity.BusSwipeMessage>
     * @Author: zxr
     * @Date: 2020/5/21 17:32
     */
    List<BusSwipeMessage> getExpenseListDetail(@Param("studentId")Long studentId,@Param("month")String month,@Param("swipeType")int swipeType);

}
