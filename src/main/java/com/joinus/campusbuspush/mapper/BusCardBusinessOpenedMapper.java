package com.joinus.campusbuspush.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.campusbuspush.entity.BusCardBusinessOpened;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;

/**
 * 业务开通详情mapper
 * @patams: 
 * @return: 
 * @Author: zxr
 * @Date: 2020/8/24 14:35
 */
@Mapper
public interface BusCardBusinessOpenedMapper  extends BaseMapper<BusCardBusinessOpened> {

    /**
     * 获取已开通业务最后结束时间
     * @patams: [studentId, productId]
     * @return: java.util.Date
     * @Author: zxr
     * @Date: 2020/8/24 17:51
     */
    Date getOpenedLastEndTime(Long studentId, Long productId);
}
