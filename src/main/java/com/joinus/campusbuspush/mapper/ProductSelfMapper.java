package com.joinus.campusbuspush.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.campusbuspush.entity.ProductSelfSub;
import org.apache.ibatis.annotations.Mapper;

import java.io.Serializable;
import java.util.List;

/**
 * @program: campus-bus-push
 * 套餐产品相关映射
 * @author: zxr
 * @create: 2020-05-19 17:15
 **/
@Mapper
public interface ProductSelfMapper extends BaseMapper<ProductSelfSub> {

    /**
     * 根据子产品id查询产品套餐详情
     * @patams: [id]
     * @return: com.joinus.campusbuspush.entity.ProductSelf
     * @Author: zxr
     * @Date: 2020/8/24 17:05
     */
    ProductSelfSub selectBySubId(Serializable id);

    /**
     * 查询业务子套餐列表
     * @patams: []
     * @return: java.util.List<com.joinus.campusbuspush.entity.ProductSelf>
     * @Author: zxr
     * @Date: 2020/5/19 17:35
     */
    List<ProductSelfSub> selectProductSubList();

    /**
     * 查询业务类型列表
     * @params: []
     * @return: java.util.List<com.joinus.campusbuspush.entity.ProductSelf>
     * @Author: jxt
     * @Date: 2020/9/2 9:26 上午
     **/
    List<ProductSelfSub> selectProductList();

    /**
     * 查询赠送业务id
     * @return
     */
    Long selectGiftProductId();
}
