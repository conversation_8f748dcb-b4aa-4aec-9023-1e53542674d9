package com.joinus.campusbuspush.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.campusbuspush.entity.pojo.request.OpenBusinessParamRequest;
import com.joinus.campusbuspush.entity.Student;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @program: campus-bus-push
 * 学生相关映射
 * @author: zxr
 * @create: 2020-05-11 17:15
 **/
@Mapper
public interface StudentMapper extends BaseMapper<Student> {

    /**
     * 查询学生和家长关系是否已存在
     * @patams: [studentId, parentId]
     * @return: java.lang.Integer
     * @Author: zxr
     * @Date: 2020/5/28 15:30
     */
    Integer selectStudentParentExist(Long studentId,Long parentId);

    /**
     * 新增学生和家长关系
     * @patams: [studentId, parentId]
     * @return: java.lang.Integer
     * @Author: zxr
     * @Date: 2020/5/28 15:30
     */
    Integer insertStudentParent(Long studentId, Long parentId, String childRelation, Integer parentSort);

    /**
     * queryStudentParentByStudentIdAndTelNum
     * @params: [studentId, telNum] 
     * @return: java.util.Map<java.lang.String,java.lang.Long>
     * @Author: jxt
     * @Date: 2020/12/9 8:40 上午
     **/
    List<Map<String, BigDecimal>> queryStudentParentByStudentIdAndTelNum(@Param("studentId") Long studentId, @Param("telNum") String telNum);

    /**
     * deleteStudentParentByStudentIdAndParentId
     * @params: [studentId, parentId] 
     * @return: void
     * @Author: jxt
     * @Date: 2020/12/9 8:41 上午
     **/
    void deleteStudentParentByStudentIdAndParentId(@Param("studentId") Long studentId, @Param("parentId") Long parentId);

    /**
     * checkStudentExistsByCardNo
     * @params [cardNo]
     * @return com.joinus.campusbuspush.entity.Student
     * <AUTHOR>
     * @date 2021/8/16 3:11 下午
     */
    Student checkStudentExistsByCardNo(String cardNo);

    /**
     * selectStudentParentMaxSort
     * @param studentId 学生id
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2023/5/10 15:36
     */
    Integer selectStudentParentMaxSort(Long studentId);
}
