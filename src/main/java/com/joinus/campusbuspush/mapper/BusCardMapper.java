package com.joinus.campusbuspush.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.campusbuspush.entity.BusCard;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公交卡相关mapper
 * @Author: zxr
 * @Date: 2020/5/14 13:53
*/
@Mapper
public interface BusCardMapper extends BaseMapper<BusCard> {

    /**
     * 获取学生下所有有效卡
     * @params: [studentId] 
     * @return: java.util.List<com.joinus.campusbuspush.entity.BusCard>
     * @Author: jxt
     * @Date: 2020/9/16 9:37 上午
     **/
    List<BusCard> getBusCardListByStudentIdAndOpenId(Long studentId, String openId);
    
    /**
     * 获取启用的卡
     * @params: [studentId, openId]
     * @return: java.util.List<com.joinus.campusbuspush.entity.BusCard>
     * @Author: jxt
     * @Date: 2020/9/16 9:36 上午
     **/
    List<BusCard> getUseingBusCardByStudentIdAndOpenId(Long studentId, String openId);
}
