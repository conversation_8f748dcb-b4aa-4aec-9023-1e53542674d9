package com.joinus.campusbuspush.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.campusbuspush.entity.BusCardBusiness;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

/**
 * 公交卡业务开通相关mapper
 * @Author: zxr
 * @Date: 2020/5/14 13:53
 */
@Mapper
public interface BusCardBusinessMapper extends BaseMapper<BusCardBusiness> {

    /**
     * 重写根据主键查询方法,带学生姓名
     * @patams: [id]
     * @return: com.joinus.campusbuspush.entity.BusCardBusiness
     * @Author: zxr
     * @Date: 2020/5/25 17:09
     */
    @Override
    BusCardBusiness selectById(Serializable id);

    /**
     * 查询该卡是否在业务有效期内且卡在启用状态
     * @patams: [cardNo]
     * @return: com.joinus.campusbuspush.entity.BusCardBusiness
     * @Author: zxr
     * @Date: 2020/5/21 17:20
     */
    List<BusCardBusiness> selectBusCardBusiness(@Param("cardNo") String cardNo, @Param("swipeType") int swipeType);

    /**
     * 查询用户下的学生列表
     * @patams: [openId]
     * @return: java.util.List<com.joinus.campusbuspush.entity.BusCardBusiness>
     * @Author: zxr
     * @Date: 2020/5/21 17:22
     */
    List<BusCardBusiness> selectBusCardStudent(@Param("openId") String openId);

    /**
     * 查询业务将到期的数据和过期数据
     * @patams: []
     * @return: java.util.List<com.joinus.campusbuspush.entity.BusCardBusiness>
     * @Author: zxr
     * @Date: 2020/5/22 11:35
     */
    List<BusCardBusiness> selectExpiringBusiness();
    
    /**
     * 根据学生id查询业务
     * @params: [studentId] 
     * @return: com.joinus.campusbuspush.entity.BusCardBusiness
     * @Author: jxt
     * @Date: 2020/9/2 10:18 上午
     **/
    BusCardBusiness selectByStudentIdAndOpenId(Long studentId, String openId);

    /**
     * 根据卡号查询余额
     * @params: [cardNo] 
     * @return: java.lang.Double
     * @Author: jxt
     * @Date: 2020/9/16 8:55 上午
     **/
    Double selectBusCardBalance(String cardNo);
}
