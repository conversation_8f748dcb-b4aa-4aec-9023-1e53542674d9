package com.joinus.campusbuspush.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.campusbuspush.entity.BusCardBusinessBinding;
import org.apache.ibatis.annotations.Mapper;

/**
 * 卡和业务绑定关系mapper
 */
@Mapper
public interface BusCardBusinessBindingMapper extends BaseMapper<BusCardBusinessBinding> {

    /**
     * selectUsingBusCard
     * @params [openId, cardNo]
     * @return com.joinus.campusbuspush.entity.BusCardBusinessBinding
     * <AUTHOR>
     * @date 2021/8/17 2:55 下午
     */
    BusCardBusinessBinding selectBindingByOpenIdAndCardNo(String openId, String cardNo);
}
