package com.joinus.campusbuspush.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.campusbuspush.entity.Coupon;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.campusbuspush.entity.pojo.request.CouponParamRequest;
import com.joinus.campusbuspush.entity.pojo.response.CouponResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-17
 */
public interface BusCardBusinessCouponMapper extends BaseMapper<Coupon> {

    /**
     * 优惠券多条件分页查询
     * @params: [request] 
     * @return: java.util.List<com.joinus.campusbuspush.entity.pojo.response.CouponResponse>
     * @Author: jxt
     * @Date: 2020/11/18 9:45 上午
     **/
    List<CouponResponse> listCoupon(IPage page,@Param("request") CouponParamRequest request);

}
