package com.joinus.campusbuspush.common;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: campus-bus-push
 * 全局常量类
 * @author: zxr
 * @create: 2020-05-13 16:09
 **/
public class GlobalConstants {

    /**
     * 逻辑删除标识:已删除
     */
    public static final Integer IS_ACTIVE_DELETE = 0;
    /**
     * 逻辑删除标识:未删除
     */
    public static final Integer IS_ACTIVE_EFFECT = 1;
    /**
     * 公交卡使用状态_未使用
     */
    public static final Integer BUS_CARD_DISABLE = 0;
    /**
     * 公交卡使用状态-启用
     */
    public static final Integer BUS_CARD_ENABLE = 1;
    /**
     * 用户关注状态 0 未关注
     */
    public static final Integer WX_UN_SUBSCRIBE_STATUS = 0;
    /**
     * 用户关注状态 1 已关注
     */
    public static final Integer WX_SUBSCRIBE_STATUS = 1;

    /**
     * 业务类型  2:乘车安
     */
    public static final String BUSINESS_TYPE = "2";

    /**
     * oauth认证类型:客户端模式
     */
    public static String GRANT_TYPE_CLIENT_CREDENTIAL = "client_credentials";
    /**
     * oauth token参数
     */
    public static String ACCESS_TOKEN = "access_token";
    /**
     * oauth scope参数
     */
    public static String OAUTH_SCOPE_READ = "read";

    /**
     * 天迈白名单接口返回成功值
     */
    public static final String TM_RESULT_SUCCESS = "9000";
    /**
     * 错误值:未签到
     */
    public static final String TM_RESULT_NOT_SIGN = "1003";
    /**
     * 错误值:未签到
     */
    public static final String TM_RESULT_APPLY_TIMEOUT = "1003";
    /**
     * 错误值:未签到
     */
    public static final String TM_RESULT_SCODE_NOT_MATCH = "1008";
    /**
     * 错误值:签到申请超时
     */
    public static final String TM_RESULT_SIGN_TIMEOUT = "1009";
    /**
     * 签到码的key
     */
    public static final String SCODE_REDIS_KEY = "SIGN_CODE";
    /**
     * 公交product_desc为0
     */
    public static final String PRODECT_DESC_BUS = "bus";
    /**
     * 进站
     **/
    public static final int DIRECTION_IN = 1;
    /**
     * 出站
     **/
    public static final int DIRECTION_OUT = 0;
    /**
     * 一卡通校验通过返回码
     **/
    public static final int YIKATONG_VALID_SUCCESS_CODE = 200;
    /**
     * 学生卡
     **/
    public static final int CARD_TYPE_STUDENT = 0;
    public static final int CARD_TYPE_OLD = 1;
    public static final int CARD_TYPE_COMMON = 9;

    /**
     * 优惠券状态
     **/
    public interface COUPON_STATUS{
        int UNEXCHANGE = 0;
        int EXCHANGED = 1;
        int USED = 2;
    }

    /**
     * 优惠券状态
     **/
    public static Map<Integer, String> COUPON_STATUS_MAP = new HashMap<>();
    static {
        COUPON_STATUS_MAP.put(0, "未兑换");
        COUPON_STATUS_MAP.put(1, "已兑换未使用");
        COUPON_STATUS_MAP.put(2, "已使用");
    }
    /**
     * 郑州地区逻辑卡号前缀
     **/
    public static final String LOGIC_CARD_NO_PREFIX = "6371";
    /**
     * 消费类型 月票
     **/
    public static final int AMOUNT_TYPE_MONTHLY_TICKET = 0;
    /**
     * 消费类型 电子钱包
     **/
    public static final int AMOUNT_TYPE_WALLET = 1;
    /**
     * 申请操作类型 开通
     **/
    public static final int APPLY_OPER_TYPE_OPEN = 0;
    /**
     * 申请操作类型 续费
     **/
    public static final int APPLY_OPER_TYPE_RENEW = 1;

    /**
     * 乘车类型转换
     */
    public static final List<String> SWIPE_TYPE_CONVERT = new ArrayList<String>(){
        {
            add(0,"B");
            add(1, "S");
        }
    };

    /**
     * 进出方向
     */
    public static final List<String> DIRECTION_CONVERT = new ArrayList<String>(){
        {
            add(DIRECTION_OUT, "O");
            add(DIRECTION_IN, "I");
        }
    };

    /**
     * 区块链模型名称
     */
    public static final String EIM_SOURCE_MODEL = "S_CON_COMMUNICATION";

    /**
     * 区块链接口请求成功结果标识
     */
    public static final String BLOCKCHAIN_RESULT_SUCCESS = "ok";

    /**
     * 失败标识
     */
    public static final String BLOCKCHAIN_RESULT_FAIL = "fail";

    /**
     * 区块解密成功标识
     */
    public static final String BLOCK_DECRYPT_SUCCESS = "1";

    /**
     * 乘车安微信用户类型
     */
    public static final Integer WX_USER_TYPE = 13;

    public interface PRODUCT_GIFTED_STATUS {
        Integer NOT_SENT = 0;
        Integer SEND = 1;
    }

}
