package com.joinus.campusbuspush.common;

/**
 * @program: campus-bus-push
 * 全局返回码
 * @author: zxr
 * @create: 2020-05-17 21:05
 **/
public class GlobalResponseCode {

    /**
     * 请求成功
     */
    public static final Integer SUCCESSFUL = 200;
    /**
     * 参数输入错误
     */
    public static final Integer ERROR_REQUEST_PARAMETER = 40000;
    /**
     * 请求处理失败
     */
    public static final Integer ERROR_REQUEST_UNSUCCESSFUL = 40001;
    /**
     * 业务未开通或已过期
     */
    public static final Integer ERROR_BUSINESS_EXPIRED = 40002;
    /**
     * 鉴权失败
     */
    public static final Integer ERROR_PERMISSIONS_DENIED = 40003;
    /**
     * 系统处理错误
     */
    public static final Integer ERROR_SYSTEM_EXCEPTION = 50001;
}
