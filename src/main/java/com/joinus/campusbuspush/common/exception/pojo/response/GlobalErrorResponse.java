package com.joinus.campusbuspush.common.exception.pojo.response;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @program: campus-bus-push
 * 异常返回模板
 * @author: zxr
 * @create: 2020-05-16 16:16
 **/
@Data
@AllArgsConstructor
public class GlobalErrorResponse {
    private int code;
    private String msg;
    private String status;

    public GlobalErrorResponse(int code,String msg){
        this.code = code;
        this.msg = msg;
        this.status = "fail";
    }
}
