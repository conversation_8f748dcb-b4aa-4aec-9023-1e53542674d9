package com.joinus.campusbuspush.common.exception.pojo.response;

import com.joinus.campusbuspush.common.exception.constant.IResponseException;
import com.joinus.campusbuspush.common.exception.constant.enums.CommonResponseEnum;
import lombok.Data;

/**
 * 基础返回结果
 * @patams:
 * @return:
 * @Author: zxr
 * @Date: 2020/6/9 10:57
 */
@Data
public class ExceptionBaseResponse {
    /**
     * 返回码
     */
    protected int code;
    /**
     * 返回消息
     */
    protected String message;

    public ExceptionBaseResponse() {
        // 默认创建成功的回应
        this(CommonResponseEnum.SUCCESS);
    }

    public ExceptionBaseResponse(IResponseException responseEnum) {
        this(responseEnum.getCode(), responseEnum.getMessage());
    }

    public ExceptionBaseResponse(int code, String message) {
        this.code = code;
        this.message = message;
    }

}
