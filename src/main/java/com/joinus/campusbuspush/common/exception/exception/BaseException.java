package com.joinus.campusbuspush.common.exception.exception;

import com.joinus.campusbuspush.common.exception.constant.IResponseException;
import lombok.Getter;

/**
 * @program: campus-bus-push
 * 自定义异常
 * @author: zxr
 * @create: 2020-05-16 16:15
 **/
@Getter
public class BaseException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    protected IResponseException responseException;
    protected Object[] args;

    public BaseException(IResponseException responseException) {
        super(responseException.getMessage());
        this.responseException = responseException;
    }

    public BaseException(int code, String msg) {
        super(msg);
        this.responseException = new IResponseException() {
            @Override
            public int getCode() {
                return code;
            }

            @Override
            public String getMessage() {
                return msg;
            }
        };
    }

    public BaseException(IResponseException responseException, Object[] args, String message) {
        super(message);
        this.responseException = responseException;
        this.args = args;
    }

    public BaseException(IResponseException responseException, Object[] args, String message, Throwable cause) {
        super(message, cause);
        this.responseException = responseException;
        this.args = args;
    }
}
