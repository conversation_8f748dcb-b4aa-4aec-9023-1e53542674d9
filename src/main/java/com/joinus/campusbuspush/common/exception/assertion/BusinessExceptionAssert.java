package com.joinus.campusbuspush.common.exception.assertion;

import com.joinus.campusbuspush.common.exception.constant.IResponseException;
import com.joinus.campusbuspush.common.exception.exception.BaseException;
import com.joinus.campusbuspush.common.exception.exception.BusinessException;

import java.text.MessageFormat;

/**
 * 业务异常断言
 * @patams: 
 * @return:
 * @Author: zxr
 * @Date: 2020/6/9 09:25
 */
public interface BusinessExceptionAssert extends IResponseException, Assert {
    @Override
    default BaseException newException(Object... args) {
        String msg = MessageFormat.format(this.getMessage(), args);
        return new BusinessException(this, args, msg);
    }

    @Override
    default BaseException newException(Throwable t, Object... args) {
        String msg = MessageFormat.format(this.getMessage(), args);
        return new BusinessException(this, args, msg, t);
    }
}
