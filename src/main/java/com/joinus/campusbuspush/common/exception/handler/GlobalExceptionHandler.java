package com.joinus.campusbuspush.common.exception.handler;

import cn.hutool.core.util.ObjectUtil;
import com.joinus.campusbuspush.common.exception.constant.enums.CommonResponseEnum;
import com.joinus.campusbuspush.common.exception.exception.ArgumentException;
import com.joinus.campusbuspush.common.exception.exception.BaseException;
import com.joinus.campusbuspush.common.exception.exception.BusinessException;
import com.joinus.campusbuspush.common.exception.pojo.response.GlobalErrorResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.ConversionNotSupportedException;
import org.springframework.beans.TypeMismatchException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;

/**
 * @program: campus-bus-push
 * 全局异常处理
 * @author: zxr
 * @create: 2020-05-16 16:07
 **/
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 自定义异常 BaseException处理
     * @patams: [e]
     * @return: GlobalErrorResponse
     * @Author: zxr
     * @Date: 2020/6/9 10:26
     */
    @ExceptionHandler(value = BaseException.class)
    public GlobalErrorResponse handleBaseException(BaseException e) {
        log.error("BaseException,message:{}",e.getMessage());
        return new GlobalErrorResponse(e.getResponseException().getCode(), getMessage(e));
    }

    /**
     * 自定义异常 ArgumentException处理
     * @patams: [e]
     * @return: GlobalErrorResponse
     * @Author: zxr
     * @Date: 2020/6/9 09:36
     */
    @ExceptionHandler(value = ArgumentException.class)
    public GlobalErrorResponse handleArgumentException(ArgumentException e) {
        String message = getMessage(e);
        log.error("ArgumentException,message:{}",message);
        return new GlobalErrorResponse(e.getResponseException().getCode(), message);
    }
    /**
     * 自定义异常 BusinessException处理
     * @patams: [e]
     * @return: GlobalErrorResponse
     * @Author: zxr
     * @Date: 2020/6/9 09:36
     */
    @ExceptionHandler(value = BusinessException.class)
    public GlobalErrorResponse handleBusinessException(BusinessException e) {
        String message = getMessage(e);
        log.error("BusinessException,message:{}",message);
        return new GlobalErrorResponse(e.getResponseException().getCode(), message);
    }

    /**
     * 进入Controller前的异常 ServletException
     * @patams: [e]
     * @return: GlobalErrorResponse
     * @Author: zxr
     * @Date: 2020/6/9 10:26
     */
    @ExceptionHandler(
            {NoHandlerFoundException.class, HttpRequestMethodNotSupportedException.class, HttpMediaTypeNotSupportedException.class, HttpMediaTypeNotAcceptableException.class, MissingPathVariableException.class, MissingServletRequestParameterException.class, TypeMismatchException.class, HttpMessageNotReadableException.class, HttpMessageNotWritableException.class, ServletRequestBindingException.class, ConversionNotSupportedException.class, MissingServletRequestPartException.class, AsyncRequestTimeoutException.class, RuntimeException.class})
    public GlobalErrorResponse handleServletException(Exception e) {
        e.printStackTrace();
        String message = e.getMessage();
        log.error("ServletException,message:{}",message);
        int code = CommonResponseEnum.SYSTEM_EXCEPTION.getCode();
        BaseException baseException = new BaseException(CommonResponseEnum.SYSTEM_EXCEPTION);
        message = getMessage(baseException);
        return new GlobalErrorResponse(code, message);

    }

    /**
     * 进入Controller前的异常 BindException
     * @patams: [e]
     * @return: GlobalErrorResponse
     * @Author: zxr
     * @Date: 2020/6/9 10:27
     */
    @ExceptionHandler(value = BindException.class)
    @ResponseBody
    public GlobalErrorResponse handleBindException(BindException e) {
        log.error("参数绑定异常:{}", e.getMessage());
        return wrapperBindingResult(e.getBindingResult());
    }

    /**
     * 进入Controller前的异常 参数校验(Valid)异常，将校验失败的所有异常组合成一条错误信息
     * @patams: [e]
     * @return: GlobalErrorResponse
     * @Author: zxr
     * @Date: 2020/6/9 10:27
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseBody
    public GlobalErrorResponse handleValidException(MethodArgumentNotValidException e) {
        log.error("参数校验异常:{}", e.getMessage());
        return wrapperBindingResult(e.getBindingResult());
    }

    /**
     * 包装绑定异常结果
     * @patams: [bindingResult]
     * @return: GlobalErrorResponse
     * @Author: zxr
     * @Date: 2020/6/9 10:53
     */
    private GlobalErrorResponse wrapperBindingResult(BindingResult bindingResult) {
        StringBuffer msg = new StringBuffer();
        for (ObjectError error : bindingResult.getAllErrors()) {
            //字段信息不返回
            /*msg.append("");
            if (error instanceof FieldError) {
                msg.append(((FieldError) error).getField()).append(": ");
            }*/
            msg.append(error.getDefaultMessage() == null ? "" : error.getDefaultMessage());
        }
        return new GlobalErrorResponse(CommonResponseEnum.VALID_ERROR.getCode(), msg.toString());
    }

    /**
     * 未知异常,统一返回处理失败
     * @patams: [e]
     * @return: GlobalErrorResponse
     * @Author: zxr
     * @Date: 2020/6/9 10:53
     */
    @ExceptionHandler(value = Exception.class)
    public GlobalErrorResponse handleException(Exception e) {
        log.error("系统处理错误,message:{}",e.getMessage());
        int code = CommonResponseEnum.SYSTEM_EXCEPTION.getCode();
        BaseException baseException = new BaseException(CommonResponseEnum.SYSTEM_EXCEPTION);
        String message = getMessage(baseException);
        return new GlobalErrorResponse(code, message);
    }

    /**
     * 获取异常信息
     * @patams: [e]
     * @return: java.lang.String
     * @Author: zxr
     * @Date: 2020/6/10 09:57
     */
    public String getMessage(BaseException e) {
        Object[] object = e.getArgs();
        if (ObjectUtil.isNotEmpty(object)) {
            return object[0].toString();
        }
        return e.getMessage();
    }

}
