package com.joinus.campusbuspush.common.exception.exception;

import com.joinus.campusbuspush.common.exception.constant.IResponseException;

/**
 * <p>参数异常</p>
 * <p>在处理业务过程中校验参数出现错误, 可以抛出该异常</p>
 * <p>编写公共代码（如工具类）时，对传入参数检查不通过时，可以抛出该异常</p>
 *
 * <AUTHOR>
 * @date 2019/5/2
 */
public class ArgumentException extends  BaseException {

    private static final long serialVersionUID = 1L;

    public ArgumentException(IResponseException responseException, Object[] args, String message) {
        super(responseException, args, message);
    }

    public ArgumentException(IResponseException responseException, Object[] args, String message, Throwable cause) {
        super(responseException, args, message, cause);
    }
}
