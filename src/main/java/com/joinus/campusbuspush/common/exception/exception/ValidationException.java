package com.joinus.campusbuspush.common.exception.exception;

import com.joinus.campusbuspush.common.exception.constant.IResponseException;

/**
 * <p>校验异常</p>
 * <p>调用接口时，参数格式不合法可以抛出该异常</p>
 *
 * <AUTHOR>
 * @date 2019/5/2
 */
public class ValidationException extends  BaseException {

    private static final long serialVersionUID = 1L;

    public ValidationException(IResponseException responseException, Object[] args, String message) {
        super(responseException, args, message);
    }

    public ValidationException(IResponseException responseException, Object[] args, String message, Throwable cause) {
        super(responseException, args, message, cause);
    }
}
