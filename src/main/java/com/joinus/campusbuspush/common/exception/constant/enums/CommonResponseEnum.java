package com.joinus.campusbuspush.common.exception.constant.enums;

import com.joinus.campusbuspush.common.exception.assertion.CommonExceptionAssert;
import com.joinus.campusbuspush.common.exception.exception.BaseException;
import com.joinus.campusbuspush.common.exception.pojo.response.ExceptionBaseResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>通用返回结果</p>
 *
 * <AUTHOR>
 * @date 2019/5/2
 */
@Getter
@AllArgsConstructor
public enum CommonResponseEnum implements CommonExceptionAssert {

    SUCCESS(200, "SUCCESS"),
    REQUEST_UNSUCCESSFUL(40001, "请求处理失败"),
    BUSINESS_EXPIRED(40002, "业务未开通或已过期"),
    PERMISSIONS_DENIED(40003, "token校验失败"),
    NOT_FOUND(40004, "未查询到相关信息"),
    SYSTEM_EXCEPTION(50001, "系统处理错误"),
    /**
     * 服务器繁忙，请稍后重试
     */
    SERVER_BUSY(9998, "服务器繁忙"),
    /**
     * 5***，一般对系统封装的工具出现异常
     */
    // Time
    DATE_NOT_NULL(5001, "日期不能为空"),
    DATETIME_NOT_NULL(5001, "时间不能为空"),
    TIME_NOT_NULL(5001, "时间不能为空"),
    DATE_PATTERN_MISMATCH(5002, "日期[%s]与格式[%s]不匹配，无法解析"),
    PATTERN_NOT_NULL(5003, "日期格式不能为空"),
    PATTERN_INVALID(5003, "日期格式[%s]无法识别"),
    VALID_ERROR(6000, "参数校验异常"),

    DECRYPT_FAILED(7001,"解密失败"),
    ;

    /**
     * 返回码
     */
    private int code;
    /**
     * 返回消息
     */
    private String message;

    /**
     * 校验返回结果是否成功
     * @param response 远程调用的响应
     */
    public static void assertSuccess(ExceptionBaseResponse response) {
        SYSTEM_EXCEPTION.assertNotNull(response);
        int code = response.getCode();
        if (CommonResponseEnum.SUCCESS.getCode() != code) {
            String msg = response.getMessage();
            throw new BaseException(code, msg);
        }
    }
}
