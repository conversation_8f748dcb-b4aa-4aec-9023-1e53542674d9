package com.joinus.campusbuspush.common.exception.exception;

import com.joinus.campusbuspush.common.exception.constant.IResponseException;

/**
 * @program: campus-bus-push
 * 业务异常处理
 * @author: zxr
 * @create: 2020-06-08 11:47
 **/
public class BusinessException extends BaseException {
    private static final long serialVersionUID = 1L;

    public BusinessException(IResponseException responseException, Object[] args, String message) {
        super(responseException, args, message);
    }

    public BusinessException(IResponseException responseException, Object[] args, String message, Throwable cause) {
        super(responseException, args, message, cause);
    }

}
