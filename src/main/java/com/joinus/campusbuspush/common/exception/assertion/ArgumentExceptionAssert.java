package com.joinus.campusbuspush.common.exception.assertion;

import com.joinus.campusbuspush.common.exception.constant.IResponseException;
import com.joinus.campusbuspush.common.exception.exception.ArgumentException;
import com.joinus.campusbuspush.common.exception.exception.BaseException;

import java.text.MessageFormat;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @date 2019/5/2
 */
public interface ArgumentExceptionAssert extends IResponseException, Assert {

    @Override
    default BaseException newException(Object... args) {
        String msg = MessageFormat.format(this.getMessage(), args);
        return new ArgumentException(this, args, msg);
    }

    @Override
    default BaseException newException(Throwable t, Object... args) {
        String msg = MessageFormat.format(this.getMessage(), args);
        return new ArgumentException(this, args, msg, t);
    }

}
