package com.joinus.campusbuspush.common.annotations;


import cn.hutool.core.util.PhoneUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * @program: new-comers
 * @description: 手机号校验
 * @author: zxr
 * @create: 2021-05-14 11:31
 **/
public class PhoneValidator implements ConstraintValidator<PhoneValidated,String> {
    @Override
    public boolean isValid(String phone, ConstraintValidatorContext constraintValidatorContext) {
        return PhoneUtil.isMobile(phone);
    }
}
