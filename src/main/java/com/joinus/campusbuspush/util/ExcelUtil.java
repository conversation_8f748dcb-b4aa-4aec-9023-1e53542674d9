package com.joinus.campusbuspush.util;

import org.apache.commons.collections.MapUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.xssf.usermodel.*;

import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description excel工具类
 * @date 2020/11/18
 */
public class ExcelUtil {
    /**
     * 创建xls单元格
     * @param list
     * @return
     */
    public static XSSFWorkbook exportExcel(List<Map<String,Object>> list, String sheetName, String[] excelHeader, String[] key) {
        XSSFRow row = null;
        XSSFSheet sheet = null;
        int index = 1;
        XSSFWorkbook workbook = new XSSFWorkbook();
        sheet = workbook.createSheet(sheetName);
        // 生成样式
        XSSFCellStyle style = workbook.createCellStyle();
        // 设置样式
        style.setFillForegroundColor(HSSFColor.LIGHT_CORNFLOWER_BLUE.index);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        // 生成字体
        XSSFFont font = workbook.createFont();
        font.setColor(HSSFColor.BLACK.index);
        font.setFontHeightInPoints((short) 12);
        // 把字体应用到当前的样式
        style.setFont(font);
        // 生成表头
        row = sheet.createRow(0);
        for (int i = 0; i < excelHeader.length; i++) {
            XSSFCell cell = row.createCell(i);
            cell.setCellValue(excelHeader[i]);
            cell.setCellStyle(style);
            sheet.setColumnWidth(i, excelHeader[i].getBytes().length * 250);
        }
        sheet = workbook.getSheet(sheetName);
        for (int i = 0; i < list.size(); i++) {
            row = sheet.createRow(index + i);
            for (int j = 0; j < key.length; j++) {
                String cellValue = MapUtils.getString(list.get(i),key[j],"");
                row.createCell(j).setCellValue(cellValue);
            }
        }

        return workbook;
    }

    public static void writeWB(OutputStream ouputStream, XSSFWorkbook wb) throws IOException {
        wb.write(ouputStream);
        ouputStream.flush();
    }
}
