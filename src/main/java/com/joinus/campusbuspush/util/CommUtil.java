package com.joinus.campusbuspush.util;

import com.joinus.campusbuspush.common.GlobalConstants;
import org.apache.commons.lang3.RandomStringUtils;

/**
 * <AUTHOR>
 * @description 公用工具类
 * @date 2020/11/19
 */
public class CommUtil {
    /**
     * 根据卡面印刻号生成逻辑卡号
     * @params: [cardNo]
     * @return: java.lang.String
     * @Author: jxt
     * @Date: 2020/11/19 11:04 上午
     **/
    public static String generateLogicCardNo(String cardNo){
        //去掉印刻号首位和末位，前边追加地区编码前缀
        return GlobalConstants.LOGIC_CARD_NO_PREFIX + cardNo.substring(1,13);
    }
    /**
     * 生成8位优惠券兑换码
     * @params: [] 
     * @return: java.lang.String
     * @Author: jxt
     * @Date: 2020/11/19 1:03 下午
     **/
    public static String generateCouponCode(){
        return RandomStringUtils.random(8, "123456789QWERTYIPASDFGHJKLZXCVBNM");
    }

    /**
     * 生成8位赠送优惠券兑换码，以AU开头
     * @params: []
     * @return: java.lang.String
     * @Author: jxt
     * @Date: 2020/11/19 1:03 下午
     **/
    public static String generateGiftCouponCode(){
        return "AU"+RandomStringUtils.random(6, "123456789QWERTYUIPASDFGHJKLZXCVBNM");
    }
}
