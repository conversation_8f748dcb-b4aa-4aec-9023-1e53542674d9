package com.joinus.campusbuspush.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.dozermapper.core.DozerBeanMapperBuilder;
import com.github.dozermapper.core.Mapper;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.BiFunction;

/**
 * 属性拷贝工具类
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2020/3/24 09:00
 */
public class MapperUtils {

    public final static Mapper mapper = DozerBeanMapperBuilder.buildDefault();

    public static <S, T> T map(S source, Class<T> destinationClass) {
        return mapper.map(source, destinationClass);
    }

    public static <S, T> T map(S source, Class<T> destinationClass, BiFunction<T, S, T> function) {
        final T map = mapper.map(source, destinationClass);
        return function.apply(map, source);
    }

    public static <S, T> List<T> mapList(Collection<S> sourceList, Class<T> destinationClass) {
        List<T> destinationList = new ArrayList<T>();
        if (sourceList == null) {
            return destinationList;
        } else {
            for (S sourceObject : sourceList) {
                T destinationObject = mapper.map(sourceObject, destinationClass);
                destinationList.add(destinationObject);
            }

            return destinationList;
        }
    }

    public static <S, T> List<T> mapList(Collection<S> sourceList, Class<T> destinationClass, BiFunction<T, S, T> function) {
        List<T> destinationList = new ArrayList<T>();
        if (sourceList == null) {
            return destinationList;
        } else {
            for (S sourceObject : sourceList) {
                T destinationObject = mapper.map(sourceObject, destinationClass);
                T apply = function.apply(destinationObject, sourceObject);
                if (apply != null) {
                    destinationList.add(apply);
                }
            }
            return destinationList;
        }
    }

    public static <S, T> Page<T> mapPage(IPage<S> source, Class<T> destinationClass) {
        Page<T> page = new Page<>();
        page.setSize(source.getSize());
        page.setCurrent(source.getCurrent());
        page.setTotal(source.getTotal());
        page.setRecords(mapList(source.getRecords(), destinationClass));
        return page;
    }

    public static <S, T> IPage<T> mapPage(IPage<S> source, Class<T> destinationClass, BiFunction<T, S, T> function) {
        Page<T> page = new Page<>();
        page.setSize(source.getSize());
        page.setCurrent(source.getCurrent());
        page.setTotal(source.getTotal());
        page.setRecords(mapList(source.getRecords(), destinationClass, function));
        return page;
    }


}
