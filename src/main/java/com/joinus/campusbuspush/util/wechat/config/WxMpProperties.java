package com.joinus.campusbuspush.util.wechat.config;

import com.joinus.campusbuspush.util.JsonUtils;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * @program: campus-bus-push
 * 微信公众号参数
 * @author: zxr
 * @create: 2020-05-14 16:34
 **/
@Data
@ConfigurationProperties(prefix = "wx")
public class WxMpProperties {

    private List<MpConfig> configs;

    @Data
    public static class MpConfig {
        /**
         * 设置微信公众号的appid
         */
        private String appId;

        /**
         * 设置微信公众号的app secret
         */
        private String secret;

        /**
         * 设置微信公众号的token
         */
        private String token;

        /**
         * 设置微信公众号的EncodingAESKey
         */
        private String aesKey;
    }

    @Override
    public String toString() {
        return JsonUtils.toJson(this);
    }

}
