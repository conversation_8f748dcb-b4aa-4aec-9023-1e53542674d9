package com.joinus.campusbuspush.util.wechat.handler;

import com.joinus.campusbuspush.service.BusCardWxUserService;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> <PERSON>(https://github.com/binarywang)
 */
@Component
@AllArgsConstructor
public class UnsubscribeHandler extends AbstractHandler {

    private BusCardWxUserService busCardWxUserService;

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage,
                                    Map<String, Object> context, WxMpService wxMpService,
                                    WxSessionManager sessionManager) {
        String openId = wxMessage.getFromUser();
        this.logger.info("取消关注用户 OPENID: " + openId);
        busCardWxUserService.unsubscribe(openId);
        return null;
    }

}
