package com.joinus.campusbuspush.util.wechat.handler;

import cn.hutool.json.JSONUtil;
import com.joinus.campusbuspush.service.BusCardWxUserService;
import com.joinus.campusbuspush.util.wechat.builder.TextBuilder;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.WxMpUserService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SubscribeHandler extends AbstractHandler {

    @Value("${wx.cuewords}")
    private String cueWords;

    private final BusCardWxUserService busCardWxUserService;
    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage,
                                    Map<String, Object> context, WxMpService weixinService,
                                    WxSessionManager sessionManager) throws WxErrorException {

        this.logger.info("新关注用户 OPENID: " + wxMessage.getFromUser());

        // 获取微信用户基本信息
        try {
            this.logger.info("用户未关注时获取到的基本信息: " + JSONUtil.toJsonStr(wxMessage));
            WxMpUserService userService = weixinService.getUserService();
            this.logger.info("userService : " + userService);
            WxMpUser userWxInfo = userService.userInfo(wxMessage.getFromUser(), null);
            this.logger.info("userWxInfo : " + userWxInfo);
            if (userWxInfo != null) {
                // 添加关注用户到本地数据库
                busCardWxUserService.subscribe(userWxInfo);
            } else {
                this.logger.warn("获取用户信息失败 {} {}", JSONUtil.toJsonStr(wxMessage), userWxInfo);
            }
        } catch (WxErrorException e) {
            this.logger.error(e.getError().getErrorMsg());
            e.printStackTrace();
            if (e.getError().getErrorCode() == 48001) {
                this.logger.info("该公众号没有获取用户信息权限！");
            }
        }


        WxMpXmlOutMessage responseResult = null;
        try {
            responseResult = this.handleSpecial(wxMessage);
        } catch (Exception e) {
            this.logger.error(e.getMessage(), e);
        }

        if (responseResult != null) {
            return responseResult;
        }

        try {
            return new TextBuilder().build(cueWords, wxMessage, weixinService);
        } catch (Exception e) {
            this.logger.error(e.getMessage(), e);
        }

        return null;
    }

    /**
     * 处理特殊请求，比如如果是扫码进来的，可以做相应处理
     */
    private WxMpXmlOutMessage handleSpecial(WxMpXmlMessage wxMessage)
        throws Exception {
        //TODO
        return null;
    }

}
