package com.joinus.campusbuspush.util;

import javax.servlet.http.HttpServletRequest;

/**
 * @program: campus-bus-push
 * 请求参数工具类
 * @author: zxr
 * @create: 2020-05-21 11:31
 **/
public class RequestUtils {

    /**
     * 获取用户IP地址
     * @patams: [request]
     * @return: java.lang.String
     * @Author: zxr
     * @Date: 2020/5/21 11:32
     */
    public static String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
