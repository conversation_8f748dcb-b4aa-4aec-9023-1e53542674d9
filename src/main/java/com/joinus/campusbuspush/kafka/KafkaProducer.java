package com.joinus.campusbuspush.kafka;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Notes
 * @date 2023/2/27 11:14
 */
@Slf4j
@Service
public class KafkaProducer {
    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;
    /**
     * 微信消息topic
     */
    @Value("${kafka.topic.wechat.message}")
    private String wechatMessageTopic;
    /**
     * 开通业务
     */
    @Value("${kafka.topic.key.wechat.message.open-business}")
    private String openBusinessKey;
    /**
     * 刷卡信息
     */
    @Value("${kafka.topic.key.wechat.message.swipe}")
    private String swipeMessageKey;
    /**
     * 即将到期提醒
     */
    @Value("${kafka.topic.key.wechat.message.expire}")
    private String aboutToExpireKey;
    /**
     * 第三方调用推送消息
     */
    @Value("${kafka.topic.key.wechat.message.third-party}")
    private String thirdPartyMessageKey;

    /**
     * 发送开通信息
     * @patams: [message]
     * @return: void
     * @Author: zxr
     * @Date: 2020/5/17 20:36
     */
    public void sendOpenProduct(final String message) {
        log.info("sendOpenProduct:{}",message);
        this.kafkaTemplate.send(wechatMessageTopic, openBusinessKey, message);
    }

    /**
     * 发送刷卡通知
     * @param message 参数
     * @return void
     * <AUTHOR>
     * @date 2021/12/17 4:30 下午
     */
    public void sendNewSwipeMessage(final String message) {
        log.info("sendNewSwipeMessage:{}",message);
        this.kafkaTemplate.send(wechatMessageTopic, swipeMessageKey, message);
    }

    /**
     * 发送即将过期提醒
     * @patams: [message]
     * @return: void
     * @Author: zxr
     * @Date: 2020/5/17 20:36
     */
    public void sendAboutToExpire(final String message) {
        log.info("sendAboutToExpire:{}",message);
        this.kafkaTemplate.send(wechatMessageTopic, aboutToExpireKey, message);
    }

    /**
     * sendMessage
     * @param message 参数
     * @return void
     * <AUTHOR>
     * @date 2021/9/23 3:45 下午
     */
    public void sendMessage(final String message) {
        log.info("sendThirdMessage:{}",message);
        this.kafkaTemplate.send(wechatMessageTopic, thirdPartyMessageKey, message);
    }
}
