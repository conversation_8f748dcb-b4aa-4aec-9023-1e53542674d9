package com.joinus.campusbuspush.kafka;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.campusbuspush.common.GlobalConstants;
import com.joinus.campusbuspush.entity.BusCardBusiness;
import com.joinus.campusbuspush.entity.BusSwipeMessage;
import com.joinus.campusbuspush.entity.ProductSelfSub;
import com.joinus.campusbuspush.entity.dto.OpenedMessageParam;
import com.joinus.campusbuspush.mapper.ProductSelfMapper;
import com.joinus.campusbuspush.mapper.SchoolMapper;
import com.joinus.campusbuspush.service.WxMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Notes
 * @date 2023/2/27 11:19
 */
@Slf4j
@Service
public class KafkaConsumer {

    @Value("${active.open.templateId}")
    private String openTemplateId;
    @Value("${active.swipe.templateId}")
    private String swipeTemplateId;
    @Value("${active.expire.templateId}")
    private String expireTemplateId;
    @Value("${active.enter.subway.templateId}")
    private String enterSubwayStationTemplateId;
    @Value("${active.out.subway.templateId}")
    private String outSubwayStationTemplateId;

    @Value("${active.open.url}")
    private String openUrl;
    @Value("${active.swipe.url}")
    private String swipeUrl;
    @Value("${active.expire.url}")
    private String expireUrl;
    @Value("${active.map.api.url}")
    private String mapApiUrl;

    /**
     * 开通业务
     */
    @Value("${kafka.topic.key.wechat.message.open-business}")
    private String openBusinessKey;
    /**
     * 刷卡信息
     */
    @Value("${kafka.topic.key.wechat.message.swipe}")
    private String swipeMessageKey;
    /**
     * 即将到期提醒
     */
    @Value("${kafka.topic.key.wechat.message.expire}")
    private String aboutToExpireKey;
    /**
     * 第三方调用推送消息
     */
    @Value("${kafka.topic.key.wechat.message.third-party}")
    private String thirdPartyMessageKey;

    private final static String company = "爱家校";
    private final static String productTitle = "乘车消费推送服务费";
    private final static String balanceRemind = "您好,您家人的公交卡余额不足,请及时充值,以免影响乘车";

    @Resource
    private WxMessageService wxMessageService;
    @Resource
    private SchoolMapper schoolMapper;
    @Resource
    private ProductSelfMapper productSelfMapper;

    @KafkaListener(topics = "${kafka.topic.wechat.message}", containerFactory = "kafkaManualAckListenerContainerFactory")
    public void consumeWechatMessage(@Payload String payload, @Header(KafkaHeaders.RECEIVED_MESSAGE_KEY) String key,
                                     Acknowledgment ack) {
        ack.acknowledge();
        if (openBusinessKey.equals(key)) {
            receiveOpenProduct(payload);
        } else if (swipeMessageKey.equals(key)) {
            receiveNewSwipeMessage(payload);
        } else if (aboutToExpireKey.equals(key)) {
            receiveExpireQueue(payload);
        } else if (thirdPartyMessageKey.equals(key)) {
            thirdMessage(payload);
        }
    }

    public void receiveOpenProduct(String msg) {
        log.info("receiveOpenProduct:{}", msg);
        JSONObject jsonObject = JSONUtil.parseObj(msg);
        BusCardBusiness busCardBusiness = JSONUtil.toBean(jsonObject, BusCardBusiness.class);
        String first = StrUtil.format("{}的家人您好,感谢您使用乘车安服务", busCardBusiness.getStudentName());
        ProductSelfSub productSelfSub = productSelfMapper.selectBySubId(busCardBusiness.getProductId());
        String validTime = productSelfSub.getPeriod()+"天";
        wxMessageService.sendMessage(new OpenedMessageParam().setTemplateId(openTemplateId)
                .setOpenId(busCardBusiness.getOpenId())
                .setFirst(first)
                .setKeyword1(productTitle)
                .setKeyword2(validTime)
                .setUrl(StrUtil.format(openUrl, busCardBusiness.getStudentId()))
        );
    }

    public void receiveSwipeMessage(String msg) {
        log.info("receiveSwipeMessage:{}", msg);
        JSONObject jsonObject = JSONUtil.parseObj(msg);
        BusSwipeMessage busSwipeMessage = JSONUtil.toBean(jsonObject, BusSwipeMessage.class);
        String swipeTime = DateUtil.format(busSwipeMessage.getSwipeTime(), "yyyy年MM月dd日 HH:mm");
        String busLine = StrUtil.concat(false, busSwipeMessage.getBusLineNo(), "(", busSwipeMessage.getBusStation(), ")");
        String region = schoolMapper.getSchoolRegionByStudentId(busSwipeMessage.getStudentId());
        String remark = "";
        int swipeType = busSwipeMessage.getSwipeType();
        if (swipeType == 0) {
            String first = StrUtil.format("您好,{}已刷卡上车",busSwipeMessage.getStudentName());
            double balance = busSwipeMessage.getBalance();
            int amountType = busSwipeMessage.getAmountType() == null ? 0 : busSwipeMessage.getAmountType();
            if (GlobalConstants.AMOUNT_TYPE_WALLET == amountType && balance <= 5) {
                remark = balanceRemind;
            }
            String mapSearchKey = busSwipeMessage.getBusLineNo() + " " + busSwipeMessage.getBusStation() + "[公交站]";
            wxMessageService.sendMessage(new OpenedMessageParam().setTemplateId(swipeTemplateId)
                    .setOpenId(busSwipeMessage.getOpenId())
                    .setFirst(first)
                    .setKeyword1(busSwipeMessage.getStudentName())
                    .setKeyword2(busLine)
                    .setColorKeyword2("#0000ff")
                    .setKeyword3(swipeTime)
                    .setKeyword4(busSwipeMessage.getCardNo())
                    .setUrl(StrUtil.format(mapApiUrl, mapSearchKey, region))
                    .setRemark(remark)
                    .setColorRemark("#ff0000")
            );
        } else {
            String mapSearchKey = busSwipeMessage.getBusLineNo() + " " + busSwipeMessage.getBusStation() + "[地铁站]";

            if (busSwipeMessage.getDirection() == GlobalConstants.DIRECTION_OUT) {
                //地铁出站
                String first = StrUtil.format("{}已出地铁站", busSwipeMessage.getStudentName());

                wxMessageService.sendMessage(new OpenedMessageParam().setTemplateId(outSubwayStationTemplateId)
                        .setOpenId(busSwipeMessage.getOpenId())
                        .setFirst(first)
                        .setKeyword1(busSwipeMessage.getStudentName())
                        .setKeyword2(busSwipeMessage.getBusStation())
                        .setColorKeyword2("#0000ff")
                        .setKeyword3(swipeTime)
                        .setKeyword4(busSwipeMessage.getCardNo())
                        .setUrl(StrUtil.format(mapApiUrl, mapSearchKey, region))
                );
            } else {
                //地铁进站
                String first = StrUtil.format("您好,{}已进入地铁站", busSwipeMessage.getStudentName());
                wxMessageService.sendMessage(new OpenedMessageParam().setTemplateId(enterSubwayStationTemplateId)
                        .setOpenId(busSwipeMessage.getOpenId())
                        .setFirst(first)
                        .setKeyword1(busSwipeMessage.getStudentName())
                        .setKeyword2(busSwipeMessage.getBusStation())
                        .setColorKeyword2("#0000ff")
                        .setKeyword3(swipeTime)
                        .setKeyword4(busSwipeMessage.getCardNo())
                        .setUrl(StrUtil.format(mapApiUrl, mapSearchKey, region))
                );

            }
        }
    }

    public void receiveExpireQueue(String msg) {
        log.info("receiveExpireQueue:{}", msg);
        JSONObject jsonObject = JSONUtil.parseObj(msg);
        BusCardBusiness busCardBusiness = JSONUtil.toBean(jsonObject, BusCardBusiness.class);
        String first = StrUtil.format("您好,您的乘车安服务即将到期,请及时续费,以免影响接收{}的刷卡信息", busCardBusiness.getStudentName());
        String validTime = StrUtil.format("有效期至 {}", DateUtil.formatDate(busCardBusiness.getBusEndTime()));
        wxMessageService.sendMessage(new OpenedMessageParam().setTemplateId(expireTemplateId)
                .setOpenId(busCardBusiness.getOpenId())
                .setFirst(first)
                .setKeyword1(company)
                .setKeyword2(productTitle)
                .setKeyword3(validTime)
                .setUrl(StrUtil.format(expireUrl, busCardBusiness.getStudentName(), busCardBusiness.getStudentId(), busCardBusiness.getId()))
        );
    }

    public void thirdMessage(String msg) {
        log.info("thirdMessage:{}", msg);
        JSONObject jsonObject = JSONUtil.parseObj(msg);
        OpenedMessageParam openedMessageParam = JSONUtil.toBean(jsonObject, OpenedMessageParam.class);
        wxMessageService.sendMessage(openedMessageParam);
    }

    public void receiveNewSwipeMessage(String msg) {
        log.info("receiveNewSwipeMessage:{}", msg);
        JSONObject jsonObject = JSONUtil.parseObj(msg);
        BusSwipeMessage busSwipeMessage = JSONUtil.toBean(jsonObject, BusSwipeMessage.class);
        String first = StrUtil.format("您好,您的家人{}乘车信息更新了~",busSwipeMessage.getStudentName());
        wxMessageService.sendMessage(new OpenedMessageParam().setTemplateId(swipeTemplateId)
                .setOpenId(busSwipeMessage.getOpenId())
                .setFirst(first)
                .setKeyword1(busSwipeMessage.getStudentName())
                .setKeyword2("乘车信息已上链")
                .setKeyword3("如需查看乘车时间和地点")
                .setKeyword4("点击查看详情↓↓↓↓↓↓")
                .setColorKeyword2("#0000ff")
                .setColorKeyword3("#0000ff")
                .setColorKeyword4("#0000ff")
                .setUrl(StrUtil.format(swipeUrl, busSwipeMessage.getStudentId(), busSwipeMessage.getTxHash()))
        );
    }
}
