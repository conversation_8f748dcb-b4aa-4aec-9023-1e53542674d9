<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.joinus.campusbuspush.mapper.StudentMapper">
    <resultMap id="studentMap" type="com.joinus.campusbuspush.entity.Student">
        <id property="id" column="id"/>
        <result property="identity" column="identity"/>
        <result property="schoolId" column="school_id"/>
        <result property="studentName" column="student_name"/>
        <result property="classId" column="class_id"/>

    </resultMap>

    <!--查询学生和家长关系表是否已存在-->
    <select id="selectStudentParentExist" resultType="java.lang.Integer">
        select count(0) from t_student_parent where student_id=#{studentId} and parent_id=#{parentId}
    </select>

    <!--插入学生家长关系表-->
    <insert id="insertStudentParent">
        insert into t_student_parent(student_id,parent_id,child_relation,parent_sort)
        values(#{studentId},#{parentId},#{childRelation},#{parentSort})
    </insert>

    <select id="queryStudentParentByStudentIdAndTelNum" resultType="java.util.Map">
        select sp.STUDENT_ID, sp.PARENT_ID
        from T_STUDENT_PARENT sp,
            T_PARENT p
        where sp.STUDENT_ID = #{studentId}
        and p.TEL_NUM = #{telNum}
        and sp.PARENT_ID = p.ID
    </select>

    <select id="checkStudentExistsByCardNo" resultType="com.joinus.campusbuspush.entity.Student">
        select * from T_STUDENT where ID in (
            select STUDENT_ID from T_BUS_CARD_BUSINESS b,T_BUS_CARD_BUSINESS_BINDING bb,T_BUS_CARD_INFO c
            where b.ID = bb.BUSINESS_ID and bb.CARD_ID = c.ID and c.CARD_NO = #{cardNo})
    </select>

    <delete id="deleteStudentParentByStudentIdAndParentId">
        delete T_STUDENT_PARENT where STUDENT_ID = #{studentId} and PARENT_ID = #{parentId}
    </delete>

    <select id="selectStudentParentMaxSort" resultType="java.lang.Integer" parameterType="java.lang.Long">
    select nvl(max(PARENT_SORT),0) from T_STUDENT_PARENT where STUDENT_ID = #{studentId}
    </select>

</mapper>
