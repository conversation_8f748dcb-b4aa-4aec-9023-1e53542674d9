<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.joinus.campusbuspush.mapper.ProductSelfMapper">

    <!--  查询业务套餐列表  -->
    <select id="selectProductSubList" resultType="com.joinus.campusbuspush.entity.ProductSelfSub">
        SELECT ts.PRODUCT_NAME,TSS.ID, TSS.PRODUCT_ID, TSS.SUB_PRODUCT_NAME, TSS.ORIGINAL_FEE, TSS.CURRENT_FEE, TSS.PERIOD, TS.PRODUCT_DESC
        FROM T_PRODUCT_SELF_SUB TSS
                 INNER JOIN T_PRODUCT_SELF TS ON TSS.PRODUCT_ID = TS.ID
        WHERE TS.PRODUCT_TYPE = 2 and TS.STATE = 1
        ORDER BY PERIOD
    </select>
    <select id="selectProductList" resultType="com.joinus.campusbuspush.entity.ProductSelfSub">
        SELECT ID PRODUCT_ID,PRODUCT_NAME,PRODUCT_DESC FROM T_PRODUCT_SELF
        WHERE PRODUCT_TYPE = 2 AND STATE = 1
        ORDER BY PRODUCT_DESC
    </select>

    <select id="selectBySubId"  resultType="com.joinus.campusbuspush.entity.ProductSelfSub">
        select tpss.ID,
               tpss.PRODUCT_ID,
               tpss.SUB_PRODUCT_NAME,
               tpss.ORIGINAL_FEE,
               tpss.CURRENT_FEE,
               tpss.PERIOD,
               tpss.IS_RELATED_BASIC_BIZ,
               tpss.GIVEN_DURATION,
               tpss.CALL_PKG_CONFIG,
               tpss.COST_PER_MINUTE,
               tpss.CALL_LIMIT_TYPE,
               tpss.CREATE_TIME,
               tpss.UPDATE_TIME,
               tps.PRODUCT_NAME,
               tps.PRODUCT_DESC
        from T_PRODUCT_SELF_SUB tpss
                 left join T_PRODUCT_SELF tps on tpss.PRODUCT_ID = tps.ID
        where tpss.ID = #{id}
    </select>

    <select id="selectGiftProductId" resultType="long">
        SELECT sub.ID FROM T_PRODUCT_SELF product, T_PRODUCT_SELF_SUB sub
        WHERE product.PRODUCT_TYPE = 2
        AND product.STATE = 1
        AND product.PRODUCT_DESC like '%bus%'
        AND sub.PRODUCT_ID = product.ID
        AND sub.PERIOD = 90
    </select>
</mapper>
