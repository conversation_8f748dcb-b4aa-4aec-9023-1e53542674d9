<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.joinus.campusbuspush.mapper.BusSwipeMessageMapper">
    <resultMap id="schoolMap" type="com.joinus.campusbuspush.entity.BusSwipeMessage">
        <id property="id" column="id"/>
        <result property="cardNo" column="card_no"/>
        <result property="openId" column="open_id"/>
        <result property="busNo" column="bus_no"/>
        <result property="busLineNo" column="bus_line_no"/>
        <result property="busStation" column="bus_station"/>
        <result property="amount" column="amount"/>
        <result property="balance" column="balance"/>
        <result property="swipeTime" column="swipe_time"/>
        <result property="amountType" column="amount_type"/>
    </resultMap>

    <!--查询六个月月卡消费汇总-->
    <select id="getExpenseListSixMouth"
            resultType="com.joinus.campusbuspush.entity.BusSwipeMessage">
        select
        m.month_time swipeTime,nvl(d.amount,0) amount
        from (select to_date(TO_CHAR(t.SWIPE_TIME, 'YYYY-MM'),'YYYY-MM') as swipeTime, count(*) as amount
        from T_BUS_SWIPE_MESSAGE t
        where t.STUDENT_ID = #{studentId}
        and t.TX_HASH is not null
        group by to_char(t.SWIPE_TIME, 'YYYY-MM')) d
        right join
        (SELECT trunc(add_months(sysdate,1-LEVEL),'mm') month_time
        FROM dual
        CONNECT BY LEVEL &lt;= 6) m
        on m.month_time = d.swipeTime
        order by m.month_time desc
    </select>

    <!--查询消费详情-->
    <select id="getExpenseListDetail"
            resultType="com.joinus.campusbuspush.entity.BusSwipeMessage">
        select t.BUS_LINE_NO ||'('||t.BUS_STATION||')' as busLineNo ,
               t.SWIPE_TIME as swipeTime,
               case when t.AMOUNT_TYPE = 1 then t.AMOUNT * 100 else 0 end as amount,
               t.DIRECTION as direction,
               t.SWIPE_TYPE as swipeType,
               t.TX_HASH as txHash
        from T_BUS_SWIPE_MESSAGE t
        where t.STUDENT_ID = #{studentId}
        and  to_char(t.SWIPE_TIME, 'YYYY-MM') = #{month}
        <if test="swipeType != 99">
            and t.SWIPE_TYPE = #{swipeType}
        </if>
        order by swipeTime desc
    </select>

</mapper>
