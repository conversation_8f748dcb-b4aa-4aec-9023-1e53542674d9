<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.joinus.campusbuspush.mapper.BusCardBusinessBindingMapper">

    <select id="selectBindingByOpenIdAndCardNo" resultType="com.joinus.campusbuspush.entity.BusCardBusinessBinding">
        select bb.CARD_ID,bb.BUSINESS_ID,bb.USE_STATUS
        from T_BUS_CARD_BUSINESS b, T_BUS_CARD_BUSINESS_BINDING bb, T_BUS_CARD_INFO c
        where b.ID = bb.BUSINESS_ID
        and bb.CARD_ID = c.ID
        and b.ISACTIVE = 1
        and b.OPEN_ID = #{openId}
        and c.CARD_NO = #{cardNo}
    </select>

</mapper>
