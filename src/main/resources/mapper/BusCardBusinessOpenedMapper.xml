<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.joinus.campusbuspush.mapper.BusCardBusinessOpenedMapper">
    <resultMap id="busCardBusinessOpened" type="com.joinus.campusbuspush.entity.BusCardBusinessOpened">

    </resultMap>

    <select id="getOpenedLastEndTime" parameterType="java.lang.Long" resultType="date">
        select max(END_TIME) END_TIME
        from T_BUS_CARD_BUSINESS_OPENED tbo
        where tbo.STUDENT_ID = #{studentId}
          and tbo.PRODUCT_ID = #{productId}
    </select>
</mapper>
