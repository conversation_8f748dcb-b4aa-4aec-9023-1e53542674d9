<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.campusbuspush.mapper.BusCardBusinessCouponMapper">

    <select id="listCoupon" parameterType="com.joinus.campusbuspush.entity.pojo.request.CouponParamRequest" resultType="com.joinus.campusbuspush.entity.pojo.response.CouponResponse">
        select c.ID,c.COUPON_CODE,c.COUPON_AMOUNT,TO_CHAR(c.CREATE_TIME,'yyyy-MM-dd HH24:mi:ss') CREATE_TIME,c.EXCHANGE_START_TIME,c.EXCHANGE_END_TIME,c.USE_START_TIME,c.<PERSON><PERSON>_E<PERSON>_TIME,
        c.COUPON_STATUS,s.STUDENT_NAME,b.<PERSON>HONE_NO,concat(p.PRODUCT_NAME,ps.SUB_PRODUCT_NAME) PRODUCT_NAME,o.TOTAL_FEE/100 TOTAL_FEE,
        CONCAT(CONCAT(TO_CHAR(c.EXCHANGE_START_TIME,'yyyy-MM-dd HH24:mi:ss'),'-'),TO_CHAR(c.EXCHANGE_END_TIME,'yyyy-MM-dd HH24:mi:ss')) EXCHANGE_TIME_PERIOD,
        CONCAT(CONCAT(TO_CHAR(c.USE_START_TIME,'yyyy-MM-dd HH24:mi:ss'),'-'),TO_CHAR(c.USE_END_TIME,'yyyy-MM-dd HH24:mi:ss')) USE_TIME_PERIOD
        from T_BUS_CARD_BUSINESS_COUPON c
        left join T_BUS_CARD_PAY_ORDER o on c.ORDER_ID = o.ID
        left join T_PRODUCT_SELF_SUB ps on o.PRODUCT_ID = ps.ID
        left join T_PRODUCT_SELF p on ps.PRODUCT_ID = p.ID
        left join T_STUDENT s on o.STUDENT_ID = s.ID
        left join T_BUS_CARD_BUSINESS b on o.BUSINESS_ID = b.ID
        <where>
            <if test="request.couponCode != null and request.couponCode != ''">
                and c.COUPON_CODE like CONCAT(UPPER(#{request.couponCode}),'%')
            </if>
            <if test="request.couponAmount != null and request.couponAmount != ''">
                and c.COUPON_AMOUNT = #{request.couponAmount}
            </if>
            <if test="request.createDateBegin != null and request.createDateBegin != ''">
                and c.CREATE_TIME &gt;= to_date(#{request.createDateBegin},'yyyy-MM-dd')
            </if>
            <if test="request.createDateEnd != null and request.createDateEnd != ''">
                and c.CREATE_TIME &lt;= to_date(concat(#{request.createDateEnd},' 23:59:59'),'yyyy-MM-dd HH24:mi:ss')
            </if>
            <if test="request.exchangeDateBegin != null and request.exchangeDateBegin != ''">
                and c.EXCHANGE_END_TIME &gt;= to_date(#{request.exchangeDateBegin},'yyyy-MM-dd')
            </if>
            <if test="request.exchangeDateEnd != null and request.exchangeDateEnd != ''">
                and c.EXCHANGE_END_TIME &lt;= to_date(concat(#{request.exchangeDateEnd},' 23:59:59'),'yyyy-MM-dd HH24:mi:ss')
            </if>
            <if test="request.useDateBegin != null and request.useDateBegin != ''">
                and c.USE_END_TIME &gt;= to_date(#{request.useDateBegin},'yyyy-MM-dd')
            </if>
            <if test="request.useDateEnd != null and request.useDateEnd != ''">
                and c.USE_END_TIME &lt;= to_date(concat(#{request.useDateEnd},' 23:59:59'),'yyyy-MM-dd HH24:mi:ss')
            </if>
            <if test="request.couponStatus != null">
                and c.COUPON_STATUS = #{request.couponStatus}
            </if>
            <if test="request.phoneNo != null and request.phoneNo != ''">
                and b.PHONE_NO = #{request.phoneNo}
            </if>
        </where>
        order by c.CREATE_TIME DESC
    </select>
</mapper>
