<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.joinus.campusbuspush.mapper.SchoolMapper">


    <select id="getSchoolRegionByStudentId" parameterType="long" resultType="string">
        select
            case
               when REGION_LEVEL = 2 then REGION_NAME
               when REGION_LEVEL = 3 then SUPER_REGION_NAME
            end regionName
        from SYS_REGION_NEW
        where REGION_ID =
              (select NEW_REGION_ID from T_SCHOOL where id =
                (select SCHOOL_ID from T_STUDENT where id = #{studentId})
                )
          and REGION_LEVEL > 1

    </select>
</mapper>
