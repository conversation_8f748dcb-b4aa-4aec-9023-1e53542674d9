<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.joinus.campusbuspush.mapper.BusCardBusinessMapper">
    <resultMap id="businessMap" type="com.joinus.campusbuspush.entity.BusCardBusiness">
        <id property="id" column="id"/>
        <result property="cardNo" column="card_no"/>
        <result property="balance" column="balance"/>
        <result property="productId" column="product_id"/>
        <result property="studentId" column="student_id"/>
        <result property="studentName" column="student_name"/>
        <result property="phoneNo" column="phone_no"/>
        <result property="openId" column="open_id"/>
        <result property="unionId" column="union_id"/>
        <result property="parentIdentity" column="parent_identity"/>
        <result property="subwayEndTime" column="subway_end_time"/>
        <result property="busEndTime" column="bus_end_time"/>
    </resultMap>
    <!--根据id查询,带上学生姓名-->
    <select id="selectById" resultType="com.joinus.campusbuspush.entity.BusCardBusiness">
        select
            s.STUDENT_NAME,
            p.*
        from
            T_BUS_CARD_BUSINESS p
        left join
            T_STUDENT s on p.STUDENT_ID = s.ID
        where
            p.ID = #{id}
            and p.ISACTIVE = 1
    </select>

    <!-- 查询该卡是否在业务有效期内且卡在启用状态 -->
    <select id="selectBusCardBusiness" parameterType="java.lang.String"  resultMap="businessMap" >
        select
            p.OPEN_ID,
            p.STUDENT_ID,
            s.STUDENT_NAME
        from
            T_BUS_CARD_INFO b
        inner join
            T_BUS_CARD_BUSINESS_BINDING bd on b.ID = bd.CARD_ID
        inner join
            T_BUS_CARD_BUSINESS p on bd.BUSINESS_ID = p.ID
        left join
            T_STUDENT s on p.STUDENT_ID = s.ID
        where
            b.CARD_NO = #{cardNo}
            and bd.USE_STATUS = 1
            and p.ISACTIVE = 1
        <if test="swipeType == 0">
            and p.BUS_END_TIME > sysdate
        </if>
        <if test="swipeType == 1">
            and p.SUBWAY_END_TIME > sysdate
        </if>
    </select>

    <!-- 查询用户已开通的卡和学生信息 -->
    <select id="selectBusCardStudent"  parameterType="java.lang.String"  resultMap="businessMap">
        select TBCB.ID, TBCB.STUDENT_ID, TS.STUDENT_NAME, TBCB.BUS_END_TIME, TBCB.SUBWAY_END_TIME
            from T_BUS_CARD_BUSINESS TBCB
                  left join T_STUDENT TS on TBCB.STUDENT_ID = TS.ID
            where TBCB.OPEN_ID = #{openId}
            and TBCB.ISACTIVE = 1
    </select>
    <select id="selectBusCardBalance" parameterType="java.lang.String" resultType="java.lang.Double">
        select nvl(t.BALANCE, 0)
        from (select tbsm.BALANCE
            from T_BUS_SWIPE_MESSAGE tbsm
            where tbsm.CARD_NO = #{cardNo}
            and tbsm.AMOUNT_TYPE = 0
        order by tbsm.SWIPE_TIME desc) t
        where rownum = 1
    </select>
    
    <select id="selectExpiringBusiness" resultMap="businessMap">
        select t.*, s.STUDENT_NAME
        from T_BUS_CARD_BUSINESS t
        left join T_STUDENT s on t.STUDENT_ID = s.ID
        where t.BUS_END_TIME is not null
        and  floor(to_date(to_char(t.BUS_END_TIME,'yyyyMMdd'),'yyyymmdd')- sysdate) in (7,3,1)
    </select>

    <select id="selectByStudentIdAndOpenId" resultMap="businessMap">
        select * from T_BUS_CARD_BUSINESS
        where student_id = #{studentId} and open_id = #{openId} and isactive = 1
    </select>

</mapper>
