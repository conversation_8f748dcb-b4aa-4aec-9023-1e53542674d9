<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.joinus.campusbuspush.mapper.BusCardMapper">
    <resultMap id="busCard" type="com.joinus.campusbuspush.entity.BusCard">

    </resultMap>
    <select id="getBusCardListByStudentIdAndOpenId" resultType="com.joinus.campusbuspush.entity.BusCard">
        SELECT TB.ID,
                TB.CARD_NO,
                TBB.USE_STATUS,
                TB.ISACTIVE,
                TB.UPDATE_TIME,
                TB.CREATE_TIME,
                TB.LOGIC_CARD_NO
         FROM T_BUS_CARD_INFO TB
                  INNER JOIN T_BUS_CARD_BUSINESS_BINDING TBB ON TB.ID = TBB.CARD_ID
                  INNER JOIN T_BUS_CARD_BUSINESS TBC ON TBB.BUSINESS_ID = TBC.ID
         WHERE TBC.STUDENT_ID = #{studentId} AND TBC.OPEN_ID = #{openId} AND TBC.ISACTIVE = 1
    </select>
    <select id="getUseingBusCardByStudentIdAndOpenId" resultType="com.joinus.campusbuspush.entity.BusCard">
        SELECT TB.ID,
                TB.CARD_NO,
                TBB.USE_STATUS,
                TB.ISACTIVE,
                TB.UPDATE_TIME,
                TB.CREATE_TIME
         FROM T_BUS_CARD_INFO TB
                  LEFT JOIN T_BUS_CARD_BUSINESS_BINDING TBB ON TB.ID = TBB.CARD_ID
                  LEFT JOIN T_BUS_CARD_BUSINESS TBC ON TBB.BUSINESS_ID = TBC.ID
         WHERE TBC.STUDENT_ID = #{studentId}
         AND TBC.OPEN_ID = #{openId}
         AND TBC.ISACTIVE = 1
         AND TBB.USE_STATUS = 1
    </select>
</mapper>
