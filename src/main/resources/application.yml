app:
  id: campus-bus-push
apollo:
  bootstrap:
    enabled: true
    eagerload:
      enabled: true
    namespaces: application,basic.common.database
spring:
  application:
    name: campus-bus-push
  cache:
    type: REDIS

  output:
    ansi:
      enabled: always

  datasource:
    druid:
      url: ${jdbc.url}
logging:
  config: classpath:logback.xml

mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.joinus.campusbuspush.entity
  global-config:
    db-config:
     id-type: input