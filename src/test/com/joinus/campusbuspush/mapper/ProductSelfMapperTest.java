package com.joinus.campusbuspush.mapper;

import com.joinus.campusbuspush.entity.ProductSelfSub;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureJdbc;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
@AutoConfigureJdbc
class ProductSelfMapperTest {
    @Resource
    ProductSelfMapper productSelfMapper;

    @Test
    void testSelect(){
        ProductSelfSub productSelfSub = productSelfMapper.selectBySubId(92L);
        System.out.println(productSelfSub.toString());
    }
}