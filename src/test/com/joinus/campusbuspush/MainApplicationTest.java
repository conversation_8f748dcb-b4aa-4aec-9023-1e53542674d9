package com.joinus.campusbuspush;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.campusbuspush.common.GlobalConstants;
import com.joinus.campusbuspush.entity.*;
import com.joinus.campusbuspush.mapper.BusCardWxUserMapper;
import com.joinus.campusbuspush.mapper.SchoolMapper;
import com.joinus.campusbuspush.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Date;

@SpringBootTest
@Slf4j
class MainApplicationTest {

    @Resource
    private SchoolMapper schoolMapper;
    @Resource
    private BusCardWxUserMapper busCardWxUserMapper;
    @Autowired
    private RedisUtil redisUtil;
    
    @Test
    void testQuery(){
        School school = new School();

        QueryWrapper<School> queryWrapper = new QueryWrapper();
        queryWrapper.like("SCHOOL_NAME","郑州");
//        List<BaseEntity> baseEntities = school.selectList(queryWrapper);
//        log.error(""+baseEntities.size());
        Page<School> page = new Page<>();
       IPage schools =  school.selectPage(page,queryWrapper);
        log.error(schools.toString());
    }

    @Test
    void testRedis(){
        String key = "zhang";
        String value = "zhangxr";
        redisUtil.set(key,value);
        System.out.println(redisUtil.get(key).toString());
    }

    @Test
    void testWeChat(){
        ProductSelfSub productSelfSub = new ProductSelfSub();
        QueryWrapper<ProductSelfSub> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("PRODUCT_TYPE","2");
        log.warn(productSelfSub.selectList(queryWrapper).toString());

    }
    @Test
    void testBus(){
        BusCard busCard = new BusCard();
//        busCard.setCardNo("123456789054321");
//        busCard.setCardPassword("123456");
//        busCard.setStatus("1");
//        busCard.setIsActive(1);
        busCard.setCreateTime(new Date());
        busCard.setId(10L);
        busCard.updateById();
        log.warn("busCard={}",busCard);
    }

    @Test
    void BusSwipe(){
        BusSwipeMessage busSwipeMessage = new BusSwipeMessage();
        busSwipeMessage.setAmount(0.8);
        busSwipeMessage.setBalance(123.0);
        busSwipeMessage.setBusNo("豫A88888");
        busSwipeMessage.setBusLineNo("B17");
        busSwipeMessage.setBusStation("火车站");
        busSwipeMessage.setCardNo("123456789012345");
        busSwipeMessage.setSwipeTime(new Date());
        System.err.println(JSONUtil.parseObj(busSwipeMessage));
    }


    @Test
    void checkSub(){
        String openId  = "ooiC5tzNGuwCSlNFOfXIbUxElC1";
        QueryWrapper<BusCardWxUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("open_id",openId);
        BusCardWxUser busCardWxUser = busCardWxUserMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(busCardWxUser)) {
            busCardWxUser = new BusCardWxUser();
            busCardWxUser.setOpenId(openId);
            busCardWxUser.setSubscribeStatus(GlobalConstants.WX_SUBSCRIBE_STATUS);
            this.busCardWxUserMapper.insert(busCardWxUser);
        } else {
            busCardWxUser.setSubscribeStatus(GlobalConstants.WX_SUBSCRIBE_STATUS);
            this.busCardWxUserMapper.update(busCardWxUser,queryWrapper);
        }
//        boolean flag = ObjectUtil.isNotEmpty(busCardWxUser);
//        if (flag){
//            log.error("不空");
//        }else{
//            log.error("空");
//        }

    }

}