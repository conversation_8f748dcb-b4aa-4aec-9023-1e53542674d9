package com.joinus.campusbuspush.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.joinus.campusbuspush.entity.WxUser;
import com.joinus.campusbuspush.mapper.WxUserMapper;
import com.joinus.campusbuspush.service.BusCardWxUserService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureJdbc;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
@AutoConfigureJdbc
public class BusCardWxUserServiceImplTest {

    @Resource
    BusCardWxUserService busCardWxUserService;
    @Resource
    WxUserMapper wxUserMapper;
    @Test
    public void delBusCardBussinessAndWxUserStudent() {
        String openId = "ooiC5t_lnHw3ZJHApF_O6b0zkF6c";
        Long studentId = 3810815L;
        busCardWxUserService.delBusCardBussinessAndWxUserStudent(openId, studentId);
    }

    @Test
    public void subscribe() {
        WxMpUser wxMpUser = new WxMpUser();
        String openId = "ooiC5t_lnHw3ZJHApF_O6b0zkF9d";
        String nickName = "柯博文";
        wxMpUser.setOpenId(openId);
        wxMpUser.setNickname(nickName);
        busCardWxUserService.subscribe(wxMpUser);
        QueryWrapper<WxUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("openid", openId);
        Assert.isTrue(nickName.equals(wxUserMapper.selectOne(queryWrapper).getNickname()));
    }

}