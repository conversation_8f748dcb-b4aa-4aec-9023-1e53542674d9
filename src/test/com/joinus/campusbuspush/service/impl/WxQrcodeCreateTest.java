package com.joinus.campusbuspush.service.impl;

import cn.hutool.core.io.FileUtil;
import com.joinus.campusbuspush.common.enums.SubChannel;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpQrCodeTicket;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureJdbc;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.File;

/**
 * @program: campus-bus-push
 * @description: 二维码生成测试类
 * @author: zxr
 * @create: 2021-11-29 15:59
 **/
@SpringBootTest
@AutoConfigureJdbc
public class WxQrcodeCreateTest {
    @Resource
    private WxMpService wxMpService;

    @Test
    void createSceneQrCode() {
        try {
            for (SubChannel subChannel : SubChannel.values()) {
                WxMpQrCodeTicket ticket = wxMpService.getQrcodeService().qrCodeCreateLastTicket(subChannel.getCode());
                File file = wxMpService.getQrcodeService().qrCodePicture(ticket);
                System.out.println(file.getAbsolutePath());
                File copyFile = FileUtil.copy(file, FileUtil.newFile("target/Downloads/" + subChannel.getName()+".jpg"),true);
                System.out.println(copyFile.getAbsolutePath());
            }
        } catch (WxErrorException e) {
            e.printStackTrace();
        }
    }
}
