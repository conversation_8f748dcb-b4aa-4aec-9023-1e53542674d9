package com.joinus.campusbuspush.service.impl;

import com.joinus.campusbuspush.service.BusCardService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureJdbc;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
@AutoConfigureJdbc
class BusCardServiceImplTest {

    @Resource
    BusCardService busCardService;
    @Test
    void getCardIdListByBusinessId() {
        List<Long> cardIdListByBusinessId = busCardService.getCardIdListByBusinessId(261L);
        System.out.println(cardIdListByBusinessId.toString());
    }
}