package com.joinus.campusbuspush.service.impl;

import com.joinus.campusbuspush.entity.BusSwipeMessage;
import com.joinus.campusbuspush.entity.Student;
import com.joinus.campusbuspush.service.BlockchainService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureJdbc;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/6/25
 */

@SpringBootTest
@AutoConfigureJdbc
public class BlockchainServiceImplTest {
    @Autowired
    private BlockchainService blockchainService;

    @Test
    public void testCreateWallet(){
        Student student = new Student();
        student.setId(3811056L);
        //blockchainService.createWallet(student);
    }

    @Test
    public void testQueryTransaction(){
        BusSwipeMessage busSwipeMessage = blockchainService.queryByTxHash(3811056L,"0xf73ae73af3c62f1d8613b4c3a73ca9b14566d0cb4cbfd80ab05e5170fcdef08e");
        System.out.println(busSwipeMessage.toString());
    }

    @Test
    public void testQueryTransactionList(){
        List<BusSwipeMessage> busSwipeMessageList = blockchainService.queryByTxHashList(3811056L, Arrays.asList("0x347b2d42c101b367462ba0ddf0f32fa1b1d158235805995b01f6b91ddf482851,0xf73ae73af3c62f1d8613b4c3a73ca9b14566d0cb4cbfd80ab05e5170fcdef08e"));
        busSwipeMessageList.forEach(busSwipeMessage -> System.out.println(busSwipeMessage.toString()));
    }

}
