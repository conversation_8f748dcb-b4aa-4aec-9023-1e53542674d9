package com.joinus.campusbuspush.service.impl;

import com.joinus.campusbuspush.entity.pojo.request.OpenBusinessParamRequest;
import com.joinus.campusbuspush.service.BusCarBusinessService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureJdbc;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
@AutoConfigureJdbc
public class BusCarBusinessServiceImplTest {
    @Resource
    private BusCarBusinessService busCarBusinessService;

    @Test
    public void testCheckBusCard() {
        OpenBusinessParamRequest request = new OpenBusinessParamRequest();
        request.setCardNo("66605003785352");
        request.setStudentName("张三");
        request.setCardType(0);
        request.setPhoneNo("***********");
        busCarBusinessService.checkBusCard(request);
    }
}